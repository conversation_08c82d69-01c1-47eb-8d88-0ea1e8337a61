<!DOCTYPE html><html  data-capo=""><head><meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<title>PayWay - Checkout</title>
<style nonce="/gfVVPRwkwv9HryTQAcXZpjD">*,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/*! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com*/*,:after,:before{border:0 solid #e5e7eb;box-sizing:border-box}:after,:before{--tw-content:""}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}body{line-height:inherit;margin:0}hr{border-top-width:1px;color:inherit;height:0}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-size:1em;font-variation-settings:normal}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{border-collapse:collapse;border-color:inherit;text-indent:0}button,input,optgroup,select,textarea{color:inherit;font-family:inherit;font-feature-settings:inherit;font-size:100%;font-variation-settings:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0}fieldset,legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{color:#9ca3af;opacity:1}input::placeholder,textarea::placeholder{color:#9ca3af;opacity:1}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{height:auto;max-width:100%}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width:640px){.container{max-width:640px}}@media (min-width:768px){.container{max-width:768px}}@media (min-width:1024px){.container{max-width:1024px}}@media (min-width:1280px){.container{max-width:1280px}}@media (min-width:1536px){.container{max-width:1536px}}.sr-only{height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;clip:rect(0,0,0,0);border-width:0;white-space:nowrap}.visible{visibility:visible}.invisible{visibility:hidden}.absolute{position:absolute}.relative{position:relative}.\!bottom-\[9px\]{bottom:9px!important}.bottom-0{bottom:0}.bottom-\[27px\]{bottom:27px}.left-0{left:0}.left-\[64px\]{left:64px}.right-0{right:0}.right-2{right:.5rem}.top-0{top:0}.top-1\/2{top:50%}.top-\[29px\]{top:29px}.z-\[1\]{z-index:1}.m-0{margin:0}.m-auto{margin:auto}.mx-auto{margin-left:auto;margin-right:auto}.my-\[4px\]{margin-bottom:4px;margin-top:4px}.my-\[8px\]{margin-bottom:8px;margin-top:8px}.\!mb-\[18px\]{margin-bottom:18px!important}.\!mb-\[2px\]{margin-bottom:2px!important}.\!mb-\[4px\]{margin-bottom:4px!important}.\!mt-\[16px\]{margin-top:16px!important}.\!mt-\[24px\]{margin-top:24px!important}.-mb-\[1px\]{margin-bottom:-1px}.-mb-\[2px\]{margin-bottom:-2px}.-mb-\[4px\]{margin-bottom:-4px}.-mb-\[6px\]{margin-bottom:-6px}.-ml-\[4px\]{margin-left:-4px}.-mt-2{margin-top:-.5rem}.-mt-\[10px\]{margin-top:-10px}.-mt-\[16px\]{margin-top:-16px}.-mt-\[1px\]{margin-top:-1px}.-mt-\[4px\]{margin-top:-4px}.-mt-\[5px\]{margin-top:-5px}.mb-0{margin-bottom:0}.mb-2{margin-bottom:.5rem}.mb-3{margin-bottom:.75rem}.mb-4{margin-bottom:1rem}.mb-5{margin-bottom:1.25rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.mb-\[10px\]{margin-bottom:10px}.mb-\[11px\]{margin-bottom:11px}.mb-\[14px\]{margin-bottom:14px}.mb-\[16px\]{margin-bottom:16px}.mb-\[24px\]{margin-bottom:24px}.mb-\[26px\]{margin-bottom:26px}.mb-\[28px\]{margin-bottom:28px}.mb-\[40px\]{margin-bottom:40px}.mb-\[4px\]{margin-bottom:4px}.mb-\[5px\]{margin-bottom:5px}.mb-\[6px\]{margin-bottom:6px}.mb-\[7px\]{margin-bottom:7px}.mb-\[8px\]{margin-bottom:8px}.ml-2{margin-left:.5rem}.ml-8{margin-left:2rem}.ml-\[8px\]{margin-left:8px}.mr-2{margin-right:.5rem}.mr-\[16px\]{margin-right:16px}.mr-\[8px\]{margin-right:8px}.mt-1{margin-top:.25rem}.mt-2{margin-top:.5rem}.mt-4{margin-top:1rem}.mt-6{margin-top:1.5rem}.mt-8{margin-top:2rem}.mt-\[10px\]{margin-top:10px}.mt-\[12px\]{margin-top:12px}.mt-\[14px\]{margin-top:14px}.mt-\[20px\]{margin-top:20px}.mt-\[24px\]{margin-top:24px}.mt-\[2px\]{margin-top:2px}.mt-\[3px\]{margin-top:3px}.mt-\[4px\]{margin-top:4px}.mt-\[5px\]{margin-top:5px}.mt-\[6px\]{margin-top:6px}.mt-\[7px\]{margin-top:7px}.mt-\[8px\]{margin-top:8px}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.h-10{height:2.5rem}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-8{height:2rem}.h-\[0px\]{height:0}.h-\[130px\]{height:130px}.h-\[20px\]{height:20px}.h-\[21px\]{height:21px}.h-\[26px\]{height:26px}.h-\[44px\]{height:44px}.h-\[56px\]{height:56px}.h-full{height:100%}.min-h-\[120px\]{min-height:120px}.min-h-\[192px\]{min-height:192px}.min-h-screen{min-height:100vh}.\!w-\[194px\]{width:194px!important}.w-10{width:2.5rem}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-8{width:2rem}.w-\[127px\]{width:127px}.w-\[177px\]{width:177px}.w-\[194px\]{width:194px}.w-\[217px\]{width:217px}.w-\[273px\]{width:273px}.w-\[310px\]{width:310px}.w-\[392px\]{width:392px}.w-\[702px\]{width:702px}.w-fit{width:-moz-fit-content;width:fit-content}.w-full{width:100%}.max-w-\[220px\]{max-width:220px}.flex-1{flex:1 1 0%}.flex-none{flex:none}.flex-grow{flex-grow:1}.-translate-y-1\/2{--tw-translate-y:-50%}.-translate-y-1\/2,.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes spin{to{transform:rotate(1turn)}}.animate-spin{animation:spin 1s linear infinite}.cursor-pointer{cursor:pointer}.resize{resize:both}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-end{align-items:flex-end}.items-center{align-items:center}.items-baseline{align-items:baseline}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-2{gap:.5rem}.gap-\[10px\]{gap:10px}.gap-\[15px\]{gap:15px}.space-y-2>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(.5rem*var(--tw-space-y-reverse));margin-top:calc(.5rem*(1 - var(--tw-space-y-reverse)))}.space-y-\[10px\]>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(10px*var(--tw-space-y-reverse));margin-top:calc(10px*(1 - var(--tw-space-y-reverse)))}.overflow-hidden{overflow:hidden}.break-words{overflow-wrap:break-word}.rounded-\[12px\]{border-radius:12px}.rounded-\[16px\]{border-radius:16px}.rounded-xl{border-radius:.75rem}.border{border-width:1px}.bg-\[\#F1F5F9\]{--tw-bg-opacity:1;background-color:rgb(241 245 249/var(--tw-bg-opacity,1))}.bg-\[\#F6FAFB\]{--tw-bg-opacity:1;background-color:rgb(246 250 251/var(--tw-bg-opacity,1))}.bg-\[\#f6f6f6\]{--tw-bg-opacity:1;background-color:rgb(246 246 246/var(--tw-bg-opacity,1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}.fill-white{fill:#fff}.p-0{padding:0}.p-4{padding:1rem}.p-6{padding:1.5rem}.p-\[16px\]{padding:16px}.p-\[24px\]{padding:24px}.p-\[26px\]{padding:26px}.p-\[8px\]{padding:8px}.px-2{padding-left:.5rem;padding-right:.5rem}.px-4{padding-left:1rem;padding-right:1rem}.px-\[0\]{padding-left:0;padding-right:0}.px-\[16px\]{padding-left:16px;padding-right:16px}.py-\[0px\]{padding-bottom:0;padding-top:0}.py-\[24px\]{padding-bottom:24px;padding-top:24px}.pb-0{padding-bottom:0}.pb-7{padding-bottom:1.75rem}.pb-\[14px\]{padding-bottom:14px}.pb-\[18px\]{padding-bottom:18px}.pb-\[20px\]{padding-bottom:20px}.pb-\[24px\]{padding-bottom:24px}.pb-\[40px\]{padding-bottom:40px}.pl-\[0\]{padding-left:0}.pr-\[16px\]{padding-right:16px}.pt-6{padding-top:1.5rem}.pt-\[16px\]{padding-top:16px}.pt-\[20px\]{padding-top:20px}.pt-\[24\]{padding-top:24}.pt-\[38px\]{padding-top:38px}.pt-\[56px\]{padding-top:56px}.pt-\[8px\]{padding-top:8px}.text-center{text-align:center}.text-right{text-align:right}.\!text-\[11px\]{font-size:11px!important}.text-\[14px\]{font-size:14px}.text-\[20px\]{font-size:20px}.text-\[34px\]{font-size:34px}.text-\[36px\]{font-size:36px}.text-s10{font-size:10px}.text-s12{font-size:12px}.text-s14{font-size:14px}.text-s16{font-size:16px}.text-s18{font-size:18px}.text-s20{font-size:20px}.text-s24{font-size:24px}.\!font-normal{font-weight:400!important}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.uppercase{text-transform:uppercase}.capitalize{text-transform:capitalize}.italic{font-style:italic}.leading-4{line-height:1rem}.leading-5{line-height:1.25rem}.leading-6{line-height:1.5rem}.leading-\[18px\]{line-height:18px}.leading-\[20px\]{line-height:20px}.leading-\[22px\]{line-height:22px}.-tracking-\[0\.28px\]{letter-spacing:-.28px}.-tracking-\[0\.32px\]{letter-spacing:-.32px}.tracking-normal{letter-spacing:0}.text-\[\#055b83\]{--tw-text-opacity:1;color:rgb(5 91 131/var(--tw-text-opacity,1))}.text-\[\#4a4a4a\]{--tw-text-opacity:1;color:rgb(74 74 74/var(--tw-text-opacity,1))}.text-\[\#575e68\]{--tw-text-opacity:1;color:rgb(87 94 104/var(--tw-text-opacity,1))}.text-\[\#606266\]{--tw-text-opacity:1;color:rgb(96 98 102/var(--tw-text-opacity,1))}.text-\[\#979797\]{--tw-text-opacity:1;color:rgb(151 151 151/var(--tw-text-opacity,1))}.text-cyan-500{--tw-text-opacity:1;color:rgb(6 182 212/var(--tw-text-opacity,1))}.text-gray-200{--tw-text-opacity:1;color:rgb(229 231 235/var(--tw-text-opacity,1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99/var(--tw-text-opacity,1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}.placeholder-\[\#758789\]::-moz-placeholder{--tw-placeholder-opacity:1;color:rgb(117 135 137/var(--tw-placeholder-opacity,1))}.placeholder-\[\#758789\]::placeholder{--tw-placeholder-opacity:1;color:rgb(117 135 137/var(--tw-placeholder-opacity,1))}.outline{outline-style:solid}.blur{--tw-blur:blur(8px)}.blur,.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1)}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.ease-out{transition-timing-function:cubic-bezier(0,0,.2,1)}@font-face{font-family:SF Pro Display;font-style:normal;font-weight:400;src:url(/fonts/SFProDisplay/SFProDisplay-Regular.ttf) format("truetype")}@font-face{font-family:SF Pro Display;font-style:normal;font-weight:500;src:url(/fonts/SFProDisplay/SFProDisplay-Semibold.ttf) format("truetype")}@font-face{font-family:SF Pro Display;font-style:normal;font-weight:700;src:url(/fonts/SFProDisplay/SFProDisplay-Bold.ttf) format("truetype")}@font-face{font-family:Hanuman;font-style:normal;font-weight:400;src:url(/fonts/Hanuman/Hanuman-Regular.ttf) format("truetype")}@font-face{font-family:Hanuman;font-style:normal;font-weight:700;src:url(/fonts/Hanuman/Hanuman-Bold.ttf) format("truetype")}@font-face{font-family:Arial;font-style:normal;font-weight:400;src:url(/fonts/Arial/Arial-Regular.ttf) format("truetype")}@font-face{font-family:Arial;font-style:normal;font-weight:600;src:url(/fonts/Arial/Arial-Medium.ttf) format("truetype")}@font-face{font-family:Arial;font-style:normal;font-weight:700;src:url(/fonts/Arial/Arial-Bold.ttf) format("truetype")}@font-face{font-family:Gothic A1;font-style:normal;font-weight:400;src:url(/fonts/Gothic-A1/GothicA1-Regular.ttf) format("truetype")}@font-face{font-family:Gothic A1;font-style:normal;font-weight:600;src:url(/fonts/Gothic-A1/GothicA1-SemiBold.ttf) format("truetype")}@font-face{font-family:Gothic A1;font-style:normal;font-weight:500;src:url(/fonts/Gothic-A1/GothicA1-medium.ttf) format("truetype")}@font-face{font-family:Gothic A1;font-style:normal;font-weight:700;src:url(/fonts/Gothic-A1/GothicA1-Bold.ttf) format("truetype")}@font-face{font-family:Myriad Pro;font-style:normal;font-weight:400;src:url(/fonts/Myriad\ Pro/MyriadPro-Regular.otf) format("opentype")}@font-face{font-family:Myriad Pro;font-style:normal;font-weight:600;src:url(/fonts/Myriad\ Pro/MyriadPro-Semibold.otf) format("opentype")}@font-face{font-family:Myriad Pro;font-style:normal;font-weight:700;src:url(/fonts/Myriad\ Pro/MyriadPro-Bold.otf) format("opentype")}@font-face{font-family:Open Sans;font-style:normal;font-weight:400;src:url(/fonts/Open\ Sans/OpenSans-Regular.ttf) format("truetype")}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;src:url(/fonts/Open\ Sans/OpenSans-Semibold.ttf) format("truetype")}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;src:url(/fonts/Open\ Sans/OpenSans-Bold.ttf) format("truetype")}@font-face{font-family:Roboto;font-style:normal;font-weight:400;src:url(/fonts/Roboto/Roboto-Regular.ttf) format("truetype")}@font-face{font-family:Roboto;font-style:normal;font-weight:600;src:url(/fonts/Roboto/Roboto-Medium.ttf) format("truetype")}@font-face{font-family:Roboto;font-style:normal;font-weight:700;src:url(/fonts/Roboto/Roboto-Bold.ttf) format("truetype")}@font-face{font-family:Source Sans Pro;font-style:normal;font-weight:400;src:url("/fonts/Source Sans Pro/SourceSansPro-Regular.ttf") format("truetype")}@font-face{font-family:Source Sans Pro;font-style:normal;font-weight:600;src:url("/fonts/Source Sans Pro/SourceSansPro-Semibold.ttf") format("truetype")}@font-face{font-family:Source Sans Pro;font-style:normal;font-weight:700;src:url("/fonts/Source Sans Pro/SourceSansPro-Bold.ttf") format("truetype")}@font-face{font-family:Battambang;font-style:normal;font-weight:400;src:url(/fonts/Battambang/Battambang-Regular.ttf) format("truetype")}@font-face{font-family:Battambang;font-style:normal;font-weight:700;src:url(/fonts/Battambang/Battambang-Bold.ttf) format("truetype")}@media (min-width:768px){.md\:w-\[392px\]{width:392px}.md\:p-6{padding:1.5rem}.md\:p-\[24px\]{padding:24px}.md\:pb-\[15px\]{padding-bottom:15px}.md\:pb-\[26px\]{padding-bottom:26px}.md\:pt-\[0\]{padding-top:0}.md\:pt-\[24px\]{padding-top:24px}.md\:pt-\[32px\]{padding-top:32px}}</style>
<style nonce="/gfVVPRwkwv9HryTQAcXZpjD">:root{--body-background:#99a8c0}body{color:#081b37}.noClick{pointer-events:none}.dashed-line{border-top:1.5px dashed #dbdbde}.text-muted{color:#8a8a8a}.text-pw-gray{color:#979797}.text-pw-red{color:#d0021b}.text-pw-black{color:#494949}h3{color:#081b37}h6{line-height:1.4}a:hover #back-button path{fill:#9ea2ae}.v-enter-active,.v-leave-active{transition:opacity .8s ease}.v-enter-from,.v-leave-to{opacity:0}.is-desktop .st-timer{margin-right:24px}.is-desktop .info-content{padding:0 8px}.is-desktop .card-form-content,.is-desktop .st-payment-option{padding:24px}.is-desktop .device-btn{margin-top:24px}input::-moz-placeholder{font-weight:400}input::placeholder{font-weight:400}.main-box-shadow{box-shadow:2px 0 8px rgba(0,0,0,.08);z-index:1}.hosted-mobile{opacity:0}.hosted-mobile .session-expired .custom-btn,.hosted-mobile .st-invalid-screen .custom-btn,.hosted-mobile .st-success-screen .custom-btn,.hosted-mobile .unable-process .custom-btn{bottom:0;left:0;margin-top:24px;position:absolute;right:0}.hosted-mobile .st-under-maintenance .info-content{padding:0 16px}.hosted-mobile-fade-out{animation:hostedMobileFadeOut 1s forwards}@keyframes hostedMobileFadeOut{0%{opacity:0}to{opacity:1}}.loading-icon{animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}.is-desktop .iframe_view .card-form-timer{position:absolute;right:48px}</style>
<style nonce="/gfVVPRwkwv9HryTQAcXZpjD">@font-face{font-family:SF Pro Display;font-style:normal;font-weight:400;src:url(/fonts/SFProDisplay/SFProDisplay-Regular.ttf) format("truetype")}@font-face{font-family:SF Pro Display;font-style:normal;font-weight:500;src:url(/fonts/SFProDisplay/SFProDisplay-Semibold.ttf) format("truetype")}@font-face{font-family:SF Pro Display;font-style:normal;font-weight:700;src:url(/fonts/SFProDisplay/SFProDisplay-Bold.ttf) format("truetype")}@font-face{font-family:Hanuman;font-style:normal;font-weight:400;src:url(/fonts/Hanuman/Hanuman-Regular.ttf) format("truetype")}@font-face{font-family:Hanuman;font-style:normal;font-weight:700;src:url(/fonts/Hanuman/Hanuman-Bold.ttf) format("truetype")}@font-face{font-family:Arial;font-style:normal;font-weight:400;src:url(/fonts/Arial/Arial-Regular.ttf) format("truetype")}@font-face{font-family:Arial;font-style:normal;font-weight:600;src:url(/fonts/Arial/Arial-Medium.ttf) format("truetype")}@font-face{font-family:Arial;font-style:normal;font-weight:700;src:url(/fonts/Arial/Arial-Bold.ttf) format("truetype")}@font-face{font-family:Gothic A1;font-style:normal;font-weight:400;src:url(/fonts/Gothic-A1/GothicA1-Regular.ttf) format("truetype")}@font-face{font-family:Gothic A1;font-style:normal;font-weight:600;src:url(/fonts/Gothic-A1/GothicA1-SemiBold.ttf) format("truetype")}@font-face{font-family:Gothic A1;font-style:normal;font-weight:500;src:url(/fonts/Gothic-A1/GothicA1-medium.ttf) format("truetype")}@font-face{font-family:Gothic A1;font-style:normal;font-weight:700;src:url(/fonts/Gothic-A1/GothicA1-Bold.ttf) format("truetype")}@font-face{font-family:Myriad Pro;font-style:normal;font-weight:400;src:url(/fonts/Myriad\ Pro/MyriadPro-Regular.otf) format("opentype")}@font-face{font-family:Myriad Pro;font-style:normal;font-weight:600;src:url(/fonts/Myriad\ Pro/MyriadPro-Semibold.otf) format("opentype")}@font-face{font-family:Myriad Pro;font-style:normal;font-weight:700;src:url(/fonts/Myriad\ Pro/MyriadPro-Bold.otf) format("opentype")}@font-face{font-family:Open Sans;font-style:normal;font-weight:400;src:url(/fonts/Open\ Sans/OpenSans-Regular.ttf) format("truetype")}@font-face{font-family:Open Sans;font-style:normal;font-weight:600;src:url(/fonts/Open\ Sans/OpenSans-Semibold.ttf) format("truetype")}@font-face{font-family:Open Sans;font-style:normal;font-weight:700;src:url(/fonts/Open\ Sans/OpenSans-Bold.ttf) format("truetype")}@font-face{font-family:Roboto;font-style:normal;font-weight:400;src:url(/fonts/Roboto/Roboto-Regular.ttf) format("truetype")}@font-face{font-family:Roboto;font-style:normal;font-weight:600;src:url(/fonts/Roboto/Roboto-Medium.ttf) format("truetype")}@font-face{font-family:Roboto;font-style:normal;font-weight:700;src:url(/fonts/Roboto/Roboto-Bold.ttf) format("truetype")}@font-face{font-family:Source Sans Pro;font-style:normal;font-weight:400;src:url("/fonts/Source Sans Pro/SourceSansPro-Regular.ttf") format("truetype")}@font-face{font-family:Source Sans Pro;font-style:normal;font-weight:600;src:url("/fonts/Source Sans Pro/SourceSansPro-Semibold.ttf") format("truetype")}@font-face{font-family:Source Sans Pro;font-style:normal;font-weight:700;src:url("/fonts/Source Sans Pro/SourceSansPro-Bold.ttf") format("truetype")}@font-face{font-family:Battambang;font-style:normal;font-weight:400;src:url(/fonts/Battambang/Battambang-Regular.ttf) format("truetype")}@font-face{font-family:Battambang;font-style:normal;font-weight:700;src:url(/fonts/Battambang/Battambang-Bold.ttf) format("truetype")}</style>
<style nonce="/gfVVPRwkwv9HryTQAcXZpjD">:root{--toastify-color-light:#fff;--toastify-color-dark:#121212;--toastify-color-info:#3498db;--toastify-color-success:#07bc0c;--toastify-color-warning:#f1c40f;--toastify-color-error:#e74c3c;--toastify-color-transparent:#ffffffb3;--toastify-icon-color-info:var(--toastify-color-info);--toastify-icon-color-success:var(--toastify-color-success);--toastify-icon-color-warning:var(--toastify-color-warning);--toastify-icon-color-error:var(--toastify-color-error);--toastify-toast-width:320px;--toastify-toast-background:#fff;--toastify-toast-min-height:64px;--toastify-toast-max-height:800px;--toastify-font-family:sans-serif;--toastify-z-index:9999;--toastify-text-color-light:#757575;--toastify-text-color-dark:#fff;--toastify-text-color-info:#fff;--toastify-text-color-success:#fff;--toastify-text-color-warning:#fff;--toastify-text-color-error:#fff;--toastify-spinner-color:#616161;--toastify-spinner-color-empty-area:#e0e0e0;--toastify-color-progress-light:linear-gradient(90deg,#4cd964,#5ac8fa,#007aff,#34aadc,#5856d6,#ff2d55);--toastify-color-progress-dark:#bb86fc;--toastify-color-progress-info:var(--toastify-color-info);--toastify-color-progress-success:var(--toastify-color-success);--toastify-color-progress-warning:var(--toastify-color-warning);--toastify-color-progress-error:var(--toastify-color-error);--toastify-color-progress-colored:#ddd}.Toastify__toast-container{box-sizing:border-box;color:#fff;padding:4px;position:fixed;transform:translate3d(0,0,var(--toastify-z-index) px);width:var(--toastify-toast-width);z-index:var(--toastify-z-index)}.Toastify__toast-container--top-left{left:1em;top:1em}.Toastify__toast-container--top-center{left:50%;top:1em;transform:translateX(-50%)}.Toastify__toast-container--top-right{right:1em;top:1em}.Toastify__toast-container--bottom-left{bottom:1em;left:1em}.Toastify__toast-container--bottom-center{bottom:1em;left:50%;transform:translateX(-50%)}.Toastify__toast-container--bottom-right{bottom:1em;right:1em}@media only screen and (max-width:480px){.Toastify__toast-container{left:0;margin:0;padding:0;width:100vw}.Toastify__toast-container--top-center,.Toastify__toast-container--top-left,.Toastify__toast-container--top-right{top:0;transform:translateX(0)}.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-right{bottom:0;transform:translateX(0)}.Toastify__toast-container--rtl{left:auto;right:0}}.Toastify__toast{border-radius:4px;box-shadow:0 1px 10px 0 #0000001a,0 2px 15px 0 #0000000d;box-sizing:border-box;cursor:pointer;direction:ltr;display:flex;font-family:var(--toastify-font-family);justify-content:space-between;margin-bottom:1rem;max-height:var(--toastify-toast-max-height);min-height:var(--toastify-toast-min-height);overflow:hidden;padding:8px;position:relative;z-index:0}.Toastify__toast--rtl{direction:rtl}.Toastify__toast-body{align-items:center;display:flex;flex:1 1 auto;margin:auto 0;padding:6px;white-space:pre-wrap}.Toastify__toast-body>div:last-child{flex:1}.Toastify__toast-icon{display:flex;flex-shrink:0;margin-inline-end:10px;width:20px}.Toastify--animate{animation-duration:.7s;animation-fill-mode:both}.Toastify--animate-icon{animation-duration:.3s;animation-fill-mode:both}@media only screen and (max-width:480px){.Toastify__toast{border-radius:0;margin-bottom:0}}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--colored.Toastify__toast--default,.Toastify__toast-theme--light{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{background:var(--toastify-color-info);color:var(--toastify-text-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{background:var(--toastify-color-success);color:var(--toastify-text-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{background:var(--toastify-color-warning);color:var(--toastify-text-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{background:var(--toastify-color-error);color:var(--toastify-text-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--default{background:var(--toastify-color-progress-colored)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning{background:var(--toastify-color-transparent)}.Toastify__close-button{align-self:flex-start;background:#0000;border:none;color:#fff;cursor:pointer;opacity:.7;outline:none;padding:0;transition:.3s ease}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentcolor;height:16px;width:14px}.Toastify__close-button:focus,.Toastify__close-button:hover{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{bottom:0;height:5px;left:0;opacity:.7;position:absolute;transform-origin:left;width:100%;z-index:var(--toastify-z-index)}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{left:auto;right:0;transform-origin:right}.Toastify__spinner{animation:Toastify__spin .65s linear infinite;border:2px solid;border-color:var(--toastify-spinner-color-empty-area);border-radius:100%;border-right-color:var(--toastify-spinner-color);box-sizing:border-box;height:20px;width:20px}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,0,0)}to{opacity:0;transform:translate3d(2000px,0,0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,0,0)}to{opacity:0;transform:translate3d(-2000px,0,0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,-10px,0)}40%,45%{opacity:1;transform:translate3d(0,20px,0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,10px,0)}40%,45%{opacity:1;transform:translate3d(0,-20px,0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--bottom-left,.Toastify__bounce-enter--top-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--bottom-right,.Toastify__bounce-enter--top-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--bottom-left,.Toastify__bounce-exit--top-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--bottom-right,.Toastify__bounce-exit--top-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__none{0%,60%,75%,90%,to{animation-duration:0;animation-timing-function:none}0%{opacity:1;transform:translateZ(0)}to{transform:translateZ(0)}}.Toastify__none-enter--bottom-center,.Toastify__none-enter--bottom-left,.Toastify__none-enter--bottom-right,.Toastify__none-enter--top-center,.Toastify__none-enter--top-left,.Toastify__none-enter--top-right{animation-name:Toastify__none}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{animation-timing-function:ease-in;opacity:0;transform:perspective(400px) rotateX(90deg)}40%{animation-timing-function:ease-in;transform:perspective(400px) rotateX(-20deg)}60%{opacity:1;transform:perspective(400px) rotateX(10deg)}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:perspective(400px)}30%{opacity:1;transform:perspective(400px) rotateX(-20deg)}to{opacity:0;transform:perspective(400px) rotateX(90deg)}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translateZ(0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translateZ(0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translateZ(0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translateZ(0)}}@keyframes Toastify__slideOutRight{0%{transform:translateZ(0)}to{transform:translate3d(110%,0,0);visibility:hidden}}@keyframes Toastify__slideOutLeft{0%{transform:translateZ(0)}to{transform:translate3d(-110%,0,0);visibility:hidden}}@keyframes Toastify__slideOutDown{0%{transform:translateZ(0)}to{transform:translate3d(0,500px,0);visibility:hidden}}@keyframes Toastify__slideOutUp{0%{transform:translateZ(0)}to{transform:translate3d(0,-500px,0);visibility:hidden}}.Toastify__slide-enter--bottom-left,.Toastify__slide-enter--top-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--bottom-right,.Toastify__slide-enter--top-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--bottom-left,.Toastify__slide-exit--top-left{animation-name:Toastify__slideOutLeft}.Toastify__slide-exit--bottom-right,.Toastify__slide-exit--top-right{animation-name:Toastify__slideOutRight}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown}@keyframes Toastify__spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}</style>
<style nonce="/gfVVPRwkwv9HryTQAcXZpjD">body,html{background-color:#fff}</style>
<style nonce="/gfVVPRwkwv9HryTQAcXZpjD">*{font-family:var(--font-family),"SF Pro Display","Battambang"!important}.text-base,.text-s14,h6,h6.text-muted,h6span,label,label.text-muted,labelspan{font-size:var(--font-size)!important}h5{font-size:var(--font-size-h5)!important}h3{font-size:var(--font-size-h3)!important}input{border-radius:var(--input-border-radius)!important}input.input{background:#fff}input.input:hover{background:#fff!important}input.input:focus{background:#fff}input:focus{border-color:var(--input-border-color)!important}button{background:var(--primary-color);border-radius:var(--input-border-radius)}button:disabled{background:#979797!important}button span{font-size:var(--font-size)!important}.btn_download_receipt{background:#fff!important;border:1px solid var(--primary-color)!important}.download_receipt{color:var(--primary-color)!important}.is-mobile .iframe_view .payment-option-container{padding-top:31px}.is-mobile .iframe_view .payment-option-qr-container{padding-top:7px}</style>
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-jK4ivhIx/wT+xC/yooIapJs01ERjiQB5pbdiuTuKvXCpr3+vr1qXJR95daEWG3ik" rel="stylesheet" href="/_nuxt/entry.DHXAGmwn.css">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-FtFCDIgcNtmIvei9vxGOr+7dLUHoFmVftO0yf4heenL++aP97y5P1o0kG55hcsQg" rel="stylesheet" href="/_nuxt/index.BFoko5W-.css">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-fqb7Mt+uxZexAEClJCTbwpUw653OiA2KQqJgmvi/CuS5IU+/bz8qWFMQdnhYZp5e" rel="stylesheet" href="/_nuxt/virtual_public.Ca85DF5t.css">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-ozEX5P8wqqMeQRFE0lE4gybmEbUp9kt6xrIto/9lLa+t32NBrksxatPWbCappMTm" rel="modulepreload" as="script" crossorigin href="/_nuxt/lWcLHQJo.js">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-Y0kX9HnaTC+ywlAjh5T21o8GKYH+/ctR9OoBnf6QXzvkL3aCWh3uX74KYQt4/UBf" rel="modulepreload" as="script" crossorigin href="/_nuxt/tLGga6vm.js">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-AaM860rpVV+lWSzacGZgNdwTzIEVRaRsKrgGqV1B6xxKi9ghEHBESVRiv8gLGLUe" rel="modulepreload" as="script" crossorigin href="/_nuxt/MfgDynSF.js">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-DUU6xFTjxR/yVEIRLreiHdaim5dDUv87Dolb+N4nUKFyp9PrQTcWgCrOjfFiKkLF" rel="modulepreload" as="script" crossorigin href="/_nuxt/D2r9xNnD.js">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-/ncKbm5VWOV1gLqaSy+YyETWVWGGtIiuO244u+bQ2bkaKKv64oZH2fw+whEq+Bzw" rel="modulepreload" as="script" crossorigin href="/_nuxt/DUHBIpD7.js">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-CFcdZg8zM2a26kVVijWccpdIP+FvLXzTCxw2CRIioggNYOWdlC/IyBkbPTguKGNe" rel="modulepreload" as="script" crossorigin href="/_nuxt/EblT3b6N.js">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-S4I+oYVrNnKe7T6ZNQCV+l8MjSI8E5oRrPDsm4mHVlZdT9uJ3groNP3kuxYEYBeu" rel="modulepreload" as="script" crossorigin href="/_nuxt/B41H1yQx.js">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-Kcn4D7lmjJuvbLUQ+nQPvdflnOjuLw3yDAVb41iwezTJmtknYqBDvf6hfxyVyzLO" rel="modulepreload" as="script" crossorigin href="/_nuxt/BP3q9d6C.js">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" rel="prefetch" as="script" crossorigin href="/_nuxt/DA1EOOqp.js">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" rel="prefetch" as="script" crossorigin href="/_nuxt/BDrwl8PC.js">
<meta name="referrer" content="no-referrer-when-downgrade">
<meta http-equiv="Permissions-Policy" content="payment=(self)">
<meta name="description" content>
<meta name="keywords" content>
<meta name="robots" content="index, follow">
<meta name="googlebot" content="index, follow">
<link nonce="/gfVVPRwkwv9HryTQAcXZpjD" rel="icon" type="image/x-icon" href="/images/favicon.ico">
<script nonce="/gfVVPRwkwv9HryTQAcXZpjD" integrity="sha384-ozEX5P8wqqMeQRFE0lE4gybmEbUp9kt6xrIto/9lLa+t32NBrksxatPWbCappMTm" type="module" src="/_nuxt/lWcLHQJo.js" crossorigin></script>
<script nonce="/gfVVPRwkwv9HryTQAcXZpjD" id="unhead:payload" type="application/json">{"title":"PayWay - Checkout"}</script></head><body><div id="__nuxt"><main id="container" class="is-desktop"><!--[--><div class="dialog-display"></div><div class="flex flex-col right-side w-full iframe_view"><!----></div><!--]--></main></div><div id="teleports"></div><script nonce="/gfVVPRwkwv9HryTQAcXZpjD" type="application/json" data-nuxt-data="nuxt-app" data-ssr="true" id="__NUXT_DATA__">[["ShallowReactive",1],{"data":2,"state":4,"once":7,"_errors":8,"serverRendered":10,"path":11,"pinia":12},["ShallowReactive",3],{},["Reactive",5],{"$snuxt-i18n-meta":6},{},["Set"],["ShallowReactive",9],{},true,"/checkout/eyJzdGF0dXMiOnsiY29kZSI6MjYsIm1lc3NhZ2UiOiJJbnZhbGlkIE1lcmNoYW50IFByb2ZpbGUuIiwicHdfdHJhbl9pZCI6IjE3NTEzMjc0MTMyOTYwMiJ9fQ%3D%3D",["Reactive",13],{"checkout":14},{"isFirstLoad":10,"checkoutData":15,"transactionSummary":16,"paymentOptions":17,"paymentOptionsGooglePay":18,"payAmount":19,"vatAmount":19,"orgVatAmount":19,"orgTotalAmount":19,"clickedStep":20,"backButton":18,"errorMessage":21,"acsData":22,"merchantInfo":23,"cardForm":24,"cardType":20,"transactionFee":25,"paymentSuccessData":26,"allowCardTypes":27,"qrFormTypes":32,"addCardSuccessUrl":20,"heightChanged":38,"cardSave":18,"isPaymentGooglePaySuccess":18,"googlePayMentData":20,"checkoutNativePath":39,"checkoutPath":40},{},{},{},false,0,"",{"code":20,"msg":20,"tran_id":20},{},{},{"saveCard":10},{},{},[28,29,30,31],"visa","mastercard","unionpay","jcb",[33,34,35,36,37],"abapay_request_qr","khqr_request_qr","abapay_khqr_request_qr","alipay_request_qr","wechat_request_qr",null,"/checkout-native","/checkout"]</script>
<script nonce="/gfVVPRwkwv9HryTQAcXZpjD">window.__NUXT__={};window.__NUXT__.config={public:{baseURL:"https://checkout-sandbox.payway.com.kh/api",SENTRY_DISABLED:"true",SENTRY_DNS:"https://<EMAIL>/5956071",SENTRY_ENVIRONMENT:"Sandbox",GOOGLE_PAY_ENVIRONMENT:"TEST",PAYWAY_DEEPLINK:"abamobilebank://ababank.com",PAYWAY_ANDRIODLINK:"https://play.google.com/store/apps/details?id=com.paygo24.ibank",PAYWAY_ANDRIOD_PACKAGE:"com.paygo24.ibank",APPLE_APP_SITE_ASSOCIATION:"https://link.payway.com.kh",GOOGLE_PAY_INTEGRITY:"",GOOGLE_ANALYTIC_UA:"UA-*********-1",i18n:{baseUrl:"",defaultLocale:"en",defaultDirection:"ltr",strategy:"no_prefix",lazy:true,rootRedirect:"",routesNameSeparator:"___",defaultLocaleRouteNameSuffix:"default",skipSettingLocaleOnNavigate:false,differentDomains:false,trailingSlash:false,configLocales:[{code:"en",files:["/app/lang/en-US.js"]},{code:"km",files:["/app/lang/km-KH.js"]},{code:"zh",files:["/app/lang/zh-CH.js"]}],locales:{en:{domain:""},km:{domain:""},zh:{domain:""}},detectBrowserLanguage:{alwaysRedirect:false,cookieCrossOrigin:false,cookieDomain:"",cookieKey:"i18n_redirected",cookieSecure:false,fallbackLocale:"",redirectOn:"root",useCookie:true},experimental:{localeDetector:"",switchLocalePathLinkSSR:false,autoImportTranslationFunctions:false},multiDomainLocales:false},device:{defaultUserAgent:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.39 Safari/537.36",enabled:true,refreshOnResize:false},gtag:{enabled:true,initMode:"auto",id:"UA-*********-1",initCommands:[],config:{},tags:[],loadingStrategy:"defer",url:"https://www.googletagmanager.com/gtag/js"}},app:{baseURL:"/",buildId:"b4922db5-5dc6-4eae-9337-54ece0f1dd16",buildAssetsDir:"/_nuxt/",cdnURL:""}}</script></body></html>