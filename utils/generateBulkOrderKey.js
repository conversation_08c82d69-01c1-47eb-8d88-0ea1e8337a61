require('module-alias/register');
const moment = require("moment")
const { BulkOrderId } = require("../models");
const db = require("../models");

module.exports = async () => {
    console.log("🚀 Generate key process is started!");
    try {
        const max = 1_000_000_000;
        let maxId = 0;

        while (maxId < max) {
            const startTime = moment();

            // Get latest bulk_order_id
            const latest = await BulkOrderId.findOne({
                order: [['bulk_order_id', 'DESC']]
            });


            const [result] = await db.sequelize.query(
                "SELECT MAX(bulk_order_id) AS max_bulk_order_id FROM bulk_order_ids",
                { type: db.sequelize.QueryTypes.SELECT }
            );

 

            maxId = parseInt(result.max_bulk_order_id)
            
            let current = latest ? parseInt(latest?.bulkOrderId || "1") : 0;


            if (current >= max) {
                console.log("✅ Reached 1B. No more inserts.");
                return;
            }

            const records = [];
            for (let i = 0; i < 1000000 && current < max; i++) {
                current++;
                records.push({
                    bulkOrderId: current.toString().padStart(10, '0'),
                    isUsed: 0,
                    usedAt: null,
                    remark: null
                });
            }

            await BulkOrderId.bulkCreate(records);
            const endTime = moment();
            const diffInSeconds = endTime.diff(startTime, 'seconds');
            console.log(`Difference is ${diffInSeconds} seconds`);

            console.log(`✅ Inserted ${records.length} new records up to ${current.toString().padStart(10, '0')}`);

        }


    } catch (error) {
        console.error("❌ Error:", error);
    }
}