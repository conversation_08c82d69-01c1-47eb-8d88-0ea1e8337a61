const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { randomUUID } = require('crypto');

// Function to generate a configured multer instance with custom path
function fileUpload(uploadPath = 'public/uploads/') {
  const storage = multer.diskStorage({
    destination: function (req, file, cb) {
      const resolvedPath = path.resolve(uploadPath);

      // Ensure the folder exists
      if (!fs.existsSync(resolvedPath)) {
        fs.mkdirSync(resolvedPath, { recursive: true });
      }

      cb(null, resolvedPath);
    },
    filename: function (req, file, cb) {
      const uniqueSuffix = randomUUID();
      const ext = path.extname(file.originalname);
      cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
    }
  });

  return multer({ storage });
}

module.exports = fileUpload;