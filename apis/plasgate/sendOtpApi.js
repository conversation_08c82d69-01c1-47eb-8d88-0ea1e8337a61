const axios = require('axios');
const normalizePhoneNumber = require("./normalizePhoneNumber")

const senderName = "bEasy";
// const senderName = "PlasGateUAT";
const baseUrl = 'https://cloudapi.plasgate.com';
const privateKey = 'BjF0iJ9K23q-s7dO_pb-32NJNBov-xDbfMzHEPPxcouoWjp-FxhQ6LnBQbAyywN1sRtb1RcEwT6axxh8Ht5k2w';
const secretKey = '$5$rounds=535000$/WfR3RHCHkDBULOu$.wi3iz4ooZooqLdsbNNaqHSJcmAeHpVztbCv77yZX57'

const sendOtpApi = async (phone, otp, lang = "en") => {

    let msg = { "content": `Your bEasy one-time password is ${otp}. Please do not share this code with anyone.` }

    switch (lang) {
        case 'en':
            msg = { "content": `Your bEasy one-time password is ${otp}. Please do not share this code with anyone.` }
            break;

        case 'km':
            msg = { "content": `លេខសម្ងាត់ម្តងតែម្ដងសម្រាប់ bEasy របស់អ្នកគឺ ${otp}។ សូមកុំចែករំលែកលេខនេះជាមួយអ្នកណាម្នាក់ឡើយ។` }
            break;
        case 'vi':
            msg = { "content": `Mật khẩu một lần bEasy của bạn là ${otp}. Vui lòng không chia sẻ mã này với bất kỳ ai.` }
            break;

        case 'cn':
            msg = { "content": `您的 bEasy 一次性密碼是 ${otp}。請不要與任何人分享此驗證碼。` }
            break;

        case 'tw':
            msg = { "content": `您的 bEasy 一次性密码是 ${otp}。请不要与任何人分享此验证码。` }
            break;

    }


    try {
        const formattedPhone = normalizePhoneNumber(phone)
        let data = JSON.stringify({
            "sender": senderName,
            "to": formattedPhone,
            ...msg,
        });

        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `${baseUrl}/rest/send?private_key=${privateKey}`,
            headers: {
                'X-Secret': secretKey,
                'Content-Type': 'application/json'
            },
            data: data
        };
        const resp = await axios.request(config);
        console.log(JSON.stringify(resp.data));

    } catch (error) {
        console.log(error);
        return error

    }

}

module.exports = sendOtpApi