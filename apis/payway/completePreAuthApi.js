
const axios = require("axios")
const moment = require('moment')
const FormData = require('form-data');
const generateHash = require('@services/payway/utils/generateHash')

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const ABA_BASE_URL = process.env.ABA_MERCHANT_PORTAL_BASE_URL
const merchantId = process.env.ABA_MERCHANT_ID
const apiKey = process.env.ABA_API_KEY

const BASE_DIR = process.env.NODE_ENV !== "production" ? __dirname : __dirname

const preauthPublicKeyPath = path.join(BASE_DIR, process.env.ABA_PREAUTH_RSA_PUBLIC_KEY);
const preauthPublicKey = fs.readFileSync(preauthPublicKeyPath, 'utf8');

const completePreAuthApi = async (tranId, amount) => {

  try {

    const data = new FormData();

    const reqTime = moment().format("YYYYMMDDHHmmss");

    data.append('request_time', reqTime);
    data.append('merchant_id', merchantId);

    const merchantAuth = {
      mc_id: merchantId,
      tran_id: tranId,
      complete_amount: amount,
    }

    // const merchantAuthEncryptedBase64 = encryptData(merchantId, tranId, amount, preauthPublicKey)
    const merchantAuthEncryptedBase64 = encryption(JSON.stringify(merchantAuth))
 

    data.append('merchant_auth', merchantAuthEncryptedBase64);
    const hash = generateHash(apiKey, [merchantAuthEncryptedBase64, reqTime, merchantId]);
    data.append('hash', hash);

    console.log("ooooooooooooooooooooooooo", {
      merchantAuth,
      request_time: reqTime,
      merchant_id: merchantId,
      merchant_auth: merchantAuthEncryptedBase64,
      hash: hash
    })

    let config = {
      method: 'POST',
      url: `${ABA_BASE_URL}/merchant-access/online-transaction/pre-auth-completion`,
      headers: {
        ...data.getHeaders()
      },
      data
    };


    const response = await axios.request(config);
    console.log("complete preauth response.data ", response.data)
    return response.data
  } catch (error) {
    console.log("error instance", JSON.stringify(error.response.data))
    return { error: error.response.data.status.message }
  }

}

function encryptData(merchant_id, tran_id, complete_amount, rsaPublicKey) {
    // Prepare data to be encrypted
    const dataObject = JSON.stringify({
        mc_id: merchant_id,
        tran_id: tran_id,
        complete_amount: complete_amount
    });

    // Maximum length for encryption chunks
    const maxLength = 117;
    let encryptedOutput = Buffer.alloc(0);

    // Encrypt data in chunks
    for (let i = 0; i < dataObject.length; i += maxLength) {
        const chunk = dataObject.substring(i, i + maxLength);
        
        try {
            const encryptedChunk = crypto.publicEncrypt(
                {
                    key: rsaPublicKey,
                    padding: crypto.constants.RSA_PKCS1_PADDING
                },
                Buffer.from(chunk, 'utf8')
            );
            encryptedOutput = Buffer.concat([encryptedOutput, encryptedChunk]);
        } catch (error) {
            throw new Error('Encryption failed for a data chunk: ' + error.message);
        }
    }

    // Encode the concatenated encrypted output in Base64
    const merchantAuth = encryptedOutput.toString('base64');
    return merchantAuth;
}


function opensslEncryption(source, publicKey) {
  const maxLength = 117; // For a 1024-bit key, RSA max encrypt length is 117 bytes
  let output = Buffer.alloc(0);

  while (source.length > 0) {
    let chunk = source.slice(0, maxLength);
    source = source.slice(maxLength);

    // Encrypt chunk
    const encryptedChunk = crypto.publicEncrypt(
      {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_PADDING, // Equivalent to PHP default
      },
      Buffer.from(chunk, 'utf8')
    );

    output = Buffer.concat([output, encryptedChunk]);
  }

  return output.toString('base64'); // Convert to Base64
}


function encryption(value) {
  return opensslEncryption(value, preauthPublicKey)
}

module.exports = completePreAuthApi