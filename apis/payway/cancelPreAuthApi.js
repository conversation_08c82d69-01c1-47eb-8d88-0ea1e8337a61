const axios = require("axios")
const moment = require('moment')
const FormData = require('form-data');
const generateHash = require('@services/payway/utils/generateHash')

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const merchantId = process.env.ABA_MERCHANT_ID
const apiKey = process.env.ABA_API_KEY

const ABA_BASE_URL = process.env.ABA_MERCHANT_PORTAL_BASE_URL
const ABA_PREAUTH_RSA_PUBLIC_KEY = process.env.ABA_PREAUTH_RSA_PUBLIC_KEY

const BASE_DIR = process.env.NODE_ENV !== "production" ? __dirname : __dirname

const publicKeyPath = path.join(BASE_DIR, ABA_PREAUTH_RSA_PUBLIC_KEY);

const publicKey = fs.readFileSync(publicKeyPath, 'utf8');

const cancelPreAuthApi = async (tranId) => {

  try {

    const data = new FormData();

    const reqTime = moment().format("YYYYMMDDHHmmss");
    data.append('request_time', reqTime);
    data.append('merchant_id', merchantId);

    const merchantAuth = { mc_id: merchantId, tran_id: tranId }
    // Convert the object to a string
    const merchantAuthString = JSON.stringify(merchantAuth);

    // Encrypt the data using the public key
    const encryptedData = crypto.publicEncrypt(
      {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_PADDING,
      },
      Buffer.from(merchantAuthString)
    );

    // Convert the encrypted data to a base64 string for transmission or storage
    const merchantAuthEncryptedBase64 = encryptedData.toString('base64');

    data.append('merchant_auth', merchantAuthEncryptedBase64);

    const hash = generateHash(apiKey, [merchantId, merchantAuthEncryptedBase64, reqTime])
    data.append('hash', hash);

    let config = {
      method: 'POST',
      url: `${ABA_BASE_URL}/merchant-access/online-transaction/pre-auth-cancellation`,
      headers: {
        ...data.getHeaders()
      },
      data
    };

    const response = await axios.request(config);

    return response.data
  } catch (error) {
    console.log("error ", error.message)

    return { error }
  }

}

module.exports = cancelPreAuthApi