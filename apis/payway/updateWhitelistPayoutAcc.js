

const axios = require("axios")
const moment = require('moment')
const FormData = require('form-data');
 
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const ABA_BASE_URL = process.env.ABA_MERCHANT_PORTAL_BASE_URL
const ABA_MERCHANT_ID = process.env.ABA_FULFILLMENT_MERCHANT_ID
const ABA_PREAUTH_RSA_PUBLIC_KEY = process.env.ABA_PREAUTH_RSA_PUBLIC_KEY

const BASE_DIR = process.env.NODE_ENV !== "production" ? __dirname : __dirname
const publicKeyPath = path.join(BASE_DIR, ABA_PREAUTH_RSA_PUBLIC_KEY);
const publicKey = fs.readFileSync(publicKeyPath, 'utf8');

const generateWhitelistPayoutAccHash = require("../../utils/payment/aba/generateWhitelistPayoutAccHash")

const updateWhitelistPayoutAcc = async (payeeAcc,status)=>{
    try {

        const data = new FormData();
     
        const reqTime = moment().format("YYYYMMDDHHmmss");
        data.append('request_time', reqTime);
        data.append('merchant_id', ABA_MERCHANT_ID );
      
        const merchantAuth = {mc_id: ABA_MERCHANT_ID, payee: payeeAcc, status }
        // Convert the object to a string
        const merchantAuthString = JSON.stringify(merchantAuth);
      
        // Encrypt the data using the public key
        const encryptedData = crypto.publicEncrypt(
          {
            key: publicKey,
            padding: crypto.constants.RSA_PKCS1_PADDING,
          },
          Buffer.from(merchantAuthString)
        );
        
        // Convert the encrypted data to a base64 string for transmission or storage
        const merchantAuthEncryptedBase64 = encryptedData.toString('base64');
      
        data.append('merchant_auth', merchantAuthEncryptedBase64);
        
        const hash = generateWhitelistPayoutAccHash(merchantAuthEncryptedBase64,reqTime);

        data.append('hash', hash);
    
        
        let config = {
          method: 'POST',
          url: `${ABA_BASE_URL}/merchant-access/whitelist-account/update-whitelist-status`,
          headers: {
            ...data.getHeaders()
          },
          data
        };
    
        const response = await axios.request(config);
     
        return response.data
      } catch (error) {
        console.log("error ", error.message)
        return { error }
      }
}

module.exports = updateWhitelistPayoutAcc