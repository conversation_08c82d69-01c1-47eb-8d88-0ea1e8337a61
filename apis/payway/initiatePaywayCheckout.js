const axios = require('axios');
const FormData = require('form-data');

const initiatePaywayCheckout = async (resp) => {
  const formData = new FormData();

  const payload = {
    hash: resp.hash,
    req_time: resp.reqTime,
    merchant_id: resp.merchantId,
    tran_id: resp.transactionId,
    amount: resp.amount,
    phone: resp.phone,
    return_url: resp.returnUrl,
    payment_option: resp.paymentOption,
    continue_success_url: resp.successUrl,
    return_deeplink: resp.returnDeeplink,
    type: resp.type,
    view_type: 'hosted_view'
  };

  // Append each field to form data
  for (const key in payload) {
    formData.append(key, payload[key]);
  }

  try {
    const response = await axios.post(
      `${resp.baseUrl}/v1/payments/purchase`,
      formData,
      {
        headers: {
          ...formData.getHeaders(), // important: include boundary
        },
      }
    );

    if(resp.paymentOption == "cards"){
      return response.request.res.responseUrl
    }else{
      return response.data
    }

  } catch (error) {
    console.error(
      'Payment request failed:',
      error.response?.data?.message || error.message
    );
    throw error;
  }
};

module.exports = initiatePaywayCheckout;