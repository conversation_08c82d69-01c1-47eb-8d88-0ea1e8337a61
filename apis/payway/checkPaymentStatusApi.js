const axios = require("axios")
const moment = require('moment')
const generateHash = require('@services/payway/utils/generateHash')
const ABA_BASE_URL = process.env.ABA_BASE_URL

const checkPaymentStatusApi = async (merchantId, apiKey, tranId) => {
  try {
    const reqTime = moment().format("YYYYMMDDHHmmss");


    const hash = generateHash(apiKey, [reqTime, merchantId, tranId]);

    const data = {
      req_time: reqTime,
      merchant_id: merchantId,
      tran_id: tranId,
      hash: hash
    };

    const config = {
      method: 'POST',
      url: `${ABA_BASE_URL}/v1/payments/check-transaction-2`,
      headers: {
        'Content-Type': 'application/json'
      },
      data
    };

    const response = await axios.request(config);
       
    return response.data;
  } catch (error) {
    console.log("catch error", error.message);
    return { error: error.message };
  }
};

module.exports = checkPaymentStatusApi