const axios = require("axios")
const moment = require('moment')
const FormData = require('form-data');
const generateHashUniversal = require('../../utils/payment/aba/generateHashUniversal')
const ABA_BASE_URL = process.env.ABA_BASE_URL
  
const getTransactionListApi = async (merchantId, apiKey) => {
  try {
    const reqTime = moment().format("YYYYMMDDHHmmss");
    const status = "APPROVED"
    const page = 1
    const pagination = 1000

    const hash = generateHashUniversal(apiKey, [reqTime, merchantId, status , page, pagination]);

    const data = {
      req_time: reqTime,
      merchant_id: merchantId,
      status,
      page,
      pagination,
      hash: hash
    };
 
    const config = {
      method: 'POST',
      url: `${ABA_BASE_URL}/v1/payments/transaction-list-2`,
      headers: {
        'Content-Type': 'application/json'
      },
      data
    };

    const response = await axios.request(config);

    return response.data;
  } catch (error) {
    console.log("catch error", error.message);
    return { error: error.message };
  }
};

module.exports = getTransactionListApi