# Node modules
node_modules/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment variables
.env
.env.local
.env.*.local

# OS-specific files
.DS_Store
Thumbs.db

# Build outputs
dist/
build/
coverage/

# Runtime files
pids/
*.pid
*.seed
*.pid.lock

# Editor configs
.vscode/
.idea/
*.swp

# Database
*.sqlite
*.sqlite3
*.db
*.db-journal

# Debug files
.nyc_output/
debug.log

# Temporary
tmp/
temp/

# Custom ignore
logs/*
!logs/.gitkeep

# Compiled views (if you're using view engines like Pug/EJS and compile)
views_compiled/