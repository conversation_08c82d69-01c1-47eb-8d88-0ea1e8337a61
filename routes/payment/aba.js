var express = require("express");
var router = express.Router({ mergeParams: true });

const checkoutPreauthController = require("@controllers/paymentGateway/aba/checkoutPreauth");
const checkoutPreauthRestController = require("@controllers/paymentGateway/aba/checkoutPreauth");

const checkoutController = require("@controllers/paymentGateway/aba/checkout");
const checkoutRestController = require("@controllers/paymentGateway/aba/checkoutRest");

const checkoutTopupRestController = require("@controllers/paymentGateway/aba/checkoutTopupRest");
const checkoutTopupController =   require("@controllers/paymentGateway/aba/checkoutTopup");

const checkPaymentStatus = require("@controllers/paymentGateway/aba/checkPaymentStatus");
const callbackSuccess = require("@controllers/paymentGateway/aba/callbackSuccess");
const callbackSuccessTopup = require("@controllers/paymentGateway/aba/callbackSuccessTopup");

const preCallbackSuccess = require("@controllers/paymentGateway/aba/preCallbackSuccess");
const subscriptionCallbackSuccess = require("@controllers/paymentGateway/aba/subscriptionCallbackSuccess");

const addWhitelistPayoutAcc = require("@controllers/paymentGateway/aba/addWhitelistPayoutAcc");
const updateWhitelistPayoutAcc = require("@controllers/paymentGateway/aba/updateWhitelistPayoutAcc");
const preCheckoutComplete = require("@controllers/paymentGateway/aba/preCheckoutComplete");

router.get("/checkout/:orderId/:paymentMethod", checkoutController);
router.get("/checkout/rest/:orderId/:paymentMethod", checkoutRestController);

router.get("/checkout/topup/:topupId/:paymentMethod", checkoutTopupController);
router.get("/checkout/topup/rest/:topupId/:paymentMethod", checkoutTopupRestController);
 

router.post("/callback/success", callbackSuccess);
router.post("/topup/callback/success", callbackSuccessTopup);

router.get("/checkout/pre-auth/:orderId/:paymentMethod", checkoutPreauthController);
router.get("/checkout/pre-auth/rest/:orderId/:paymentMethod", checkoutPreauthRestController);

router.post("/callback/pre-auth/success", preCallbackSuccess);
router.post("/pre-auth/complete", preCheckoutComplete);

 
router.post("/whitelist/payout/account/add", addWhitelistPayoutAcc);
router.post("/whitelist/payout/account/update", updateWhitelistPayoutAcc);

router.post("/:token/:shopId/:packageId/subscription/callback/success", subscriptionCallbackSuccess);
router.post("/status/check", checkPaymentStatus);

module.exports = router;