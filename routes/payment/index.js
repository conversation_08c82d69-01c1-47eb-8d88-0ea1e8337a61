var express = require("express");
var router = express.Router();
const fileUpload = require("@utils/fileUpload")

const abaRouter = require("./aba");

const uploadReceiptController = require("@controllers/paymentGateway/uploadReceipt");

router.use("/aba", abaRouter);
router.post("/receipt/upload",
    fileUpload('public/uploads/receipts').fields([
        { name: 'receiptFile', maxCount: 1 }
    ]),
    uploadReceiptController
);


module.exports = router;
