const  express = require('express');
const  router = express.Router();
// const validateRequestPayload = require("./validation")
// const userSchema = require("./validation/userSchema")
 
 const { authAdmin } = require("@authAdmin")


const listBannerController = require("@controllers/admin/banner/listBanner")
const createBannerController = require("@controllers/admin/banner/createBanner")
 const updateBannerController = require("@controllers/admin/banner/updateBanner")
 

router.get('/list', authAdmin , listBannerController);
router.post('/create',authAdmin , createBannerController);
router.post('/update', authAdmin , updateBannerController);
  
 
module.exports = router;
