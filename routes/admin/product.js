const express = require('express');
const router = express.Router();
// const validateRequestPayload = require("./validation")
// const userSchema = require("./validation/userSchema")

const { authAdmin } = require("@authAdmin")

const listProductController = require("@controllers/admin/product/listProduct")
const createProductController = require("@controllers/admin/product/createProduct")
// const updateCategoryController = require("controllers/admin/category/updateCategory")

router.get('/list', authAdmin, listProductController);
router.post('/create', authAdmin , createProductController);
// router.post('/update', updateCategoryController);

module.exports = router;
