const express = require('express');
const router = express.Router();
// const validateRequestPayload = require("./validation")
// const userSchema = require("./validation/userSchema")

const { authAdmin } = require("@authAdmin")

const listCategoryController = require("@controllers/admin/category/listCategory")
const createCategoryController = require("@controllers/admin/category/createCategory")
const updateCategoryController = require("@controllers/admin/category/updateCategory")
const rearrangeCategoryController = require("@controllers/admin/category/rearrangeCategory")

router.get('/list', authAdmin, listCategoryController);
router.post('/create', authAdmin, createCategoryController);
router.post('/update', authAdmin, updateCategoryController);
router.post('/rearrange', authAdmin, rearrangeCategoryController);


module.exports = router;
