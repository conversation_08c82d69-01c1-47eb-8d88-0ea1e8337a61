const express = require('express');
const router = express.Router();
const fileUpload = require("@utils/fileUpload")
// const validateRequestPayload = require("./validation")
// const userSchema = require("./validation/userSchema")

const { authAdmin } = require("@authAdmin")

const createAnnouncementController = require("@controllers/admin/announcement/createAnnouncement")

router.post('/create',
    authAdmin,
    fileUpload().fields([
        { name: 'thumbnailUrl', maxCount: 1 },
        { name: 'bannerUrlEn', maxCount: 1 },
        { name: 'bannerUrlKh', maxCount: 1 },
        { name: 'bannerUrlZh', maxCount: 1 }
    ]),
    createAnnouncementController);

module.exports = router;