const express = require('express');
const router = express.Router();

const authRouter = require('./auth');
const bannerRouter = require('./banner');
const categoryRouter = require('./category');
const productRouter = require('./product');
const announcementRouter = require('./announcement');
const couponRouter = require('./coupon');
const orderRouter = require("./order")
const profileRouter = require("./profile")

router.use('/auth', authRouter);
router.use('/profile', profileRouter);
router.use('/banner', bannerRouter);
router.use('/category', categoryRouter);
router.use('/product', productRouter);
router.use('/announcement', announcementRouter);
router.use('/coupon', couponRouter);
router.use('/order', orderRouter);


module.exports = router;
