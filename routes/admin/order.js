const express = require('express');
const router = express.Router();
// const validateRequestPayload = require("./validation")
// const userSchema = require("./validation/userSchema")

const { authAdmin } = require("@authAdmin")

const updateOrderStatusController = require("@controllers/admin/order/uddateOrderStatus")
const listOrderController = require("@controllers/admin/order/listOrder")
const getOrderDetailController = require("@controllers/admin/order/getOrderDetail")


router.post('/status/update',authAdmin, updateOrderStatusController);
router.get('/list', authAdmin, listOrderController);
router.get('/:bulkOrderId/detail',authAdmin, getOrderDetailController );


module.exports = router;
