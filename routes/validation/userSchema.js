const Joi = require('joi');

// 🧱 Base fields
const userFields = {
    username: Joi.string().min(8).max(20),
    password: Joi.string().min(6),
    firstName: Joi.string().max(50),
    lastName: Joi.string().max(50),
    language: Joi.string()
       .lowercase()
        .valid('en', 'km', 'zh-cn', 'zh-tw', 'vi')
        .max(7),
    email: Joi.string().email().optional(),
    referralCode: Joi.string().optional(),
    otp: Joi.string()
        .pattern(/^\d{6}$/)
        .required()
        .messages({
            'string.pattern.base': 'OTP must be a 6-digit number',
            'string.empty': 'OTP is required'
        })
};

// 📦 Schema variants by action
const userSchema = {
    create: Joi.object({
        username: userFields.username.required(),
        firstName: userFields.firstName.required(),
        lastName: userFields.lastName.required(),
        email: userFields.email,
        referralCode: userFields.referralCode,
    }),

    update: Joi.object({
        username: userFields.username.optional(),
        email: userFields.email.optional(),
        language: userFields.language.optional(),
        firstName: userFields.firstName.optional(),
        lastName: userFields.lastName.optional(),
    }).min(1), // require at least one field
    updateLanguage: Joi.object({
        lang: userFields.language.required(),
    }).min(1),
    login: Joi.object({
        username: userFields.username.required(),
        password: userFields.password.required()
    })
};

module.exports = userSchema;