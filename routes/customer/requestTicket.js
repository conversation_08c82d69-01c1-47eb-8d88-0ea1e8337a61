const express = require('express');
const router = express.Router();
// const validateRequestPayload = require("./validation")
// const userSchema = require("./validation/userSchema")


const createRequestTicketController = require("@controllers/customer/requestTicket/createRequestTicket")
const listRequestTicketController = require("@controllers/customer/requestTicket/listRequestTicket")
const requestDeleteAccountController = require("@controllers/customer/requestTicket/requestDeleteAccount")

router.get('/list', listRequestTicketController);
router.post('/create', createRequestTicketController);
router.post('/delete/account', requestDeleteAccountController);

module.exports = router;
