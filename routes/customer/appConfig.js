var express = require('express');
var router = express.Router();

const getAppVersionController = require("@controllers/customer/config/getAppVersion")
const getAppUpdateStatusController = require("@controllers/customer/config/getAppUpdateStatus")
const getSystemConfigController = require("@controllers/customer/config/getSystemConfig")

 router.get('/app/version', getAppVersionController );
 router.get('/app-update-status/:phone/:version', getAppUpdateStatusController );
 router.get('/app/system-config/:category', getSystemConfigController );

module.exports = router;
