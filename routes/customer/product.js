const express = require('express');
const router = express.Router();
// const validateRequestPayload = require("./validation")
// const userSchema = require("./validation/userSchema")

const getProductDetailController = require("@controllers/customer/product/getProductDetail")
const getProductOptionController = require("@controllers/customer/product/getProductOptions")
const getProductOptionV2Controller = require("@controllers/customer/product/getProductOptionsV2")
const getProductAddonController = require("@controllers/customer/product/getProductAddOn")

 
router.get('/:id/detail', getProductDetailController);
router.get('/:id/options', getProductOptionController);
 
router.get('/v2/:id/options', getProductOptionV2Controller);
router.get('/option/:id/product-add-on', getProductAddonController);

module.exports = router;
