var express = require('express');
var router = express.Router();

const sendOtpController = require("@controllers/customer/auth/sendOtp")
const createAccountController = require("@controllers/customer/auth/createAccount")
const loginController = require("@controllers/customer/auth/login")
// const loginWithPasswordController = require("@controllers/customer/auth/loginWithPassword")
const checkAccountStatusController = require("@controllers/customer/auth/checkAccountStatus")

 
router.post('/send/otp', sendOtpController );
router.post('/account/create', createAccountController );
router.post('/account/login', loginController );
// router.post('/account/login/password', loginWithPasswordController );
router.get('/account/:username/status/check', checkAccountStatusController );
router.get('/appstore/review-status/check', (req,res,next)=>{

    const status = true;
    let message = "";
    if(status){
        message = "App is under reviewed from Appstore, please show login screen with password"
    }else{
        message = "App is already approved in Appstore, please show login screen with OTP"
    }

    return res.json({status, message})
} );

module.exports = router;
