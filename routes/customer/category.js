var express = require('express');
var router = express.Router();

const listCategoryController = require("@controllers/customer/category/listCategory")
const getCategoryProductController = require("@controllers/customer/category/getCategoryProduct")
const getProductAddOnV2Controller = require("@controllers/customer/product/getProductAddOnV2")
const getProductAddOnItemV2Controller = require("@controllers/customer/product/getProductAddOnItemV2")

const getProductEquipmentController = require("@controllers/customer/product/getProductEquipment")


router.get('/list', listCategoryController );
router.get('/:id/product/list', getCategoryProductController );
router.get('/:id/product-add-on', getProductAddOnV2Controller );
router.get('/:id/product-add-on/:parentId/items', getProductAddOnItemV2Controller );
router.get('/:id/equipments', getProductEquipmentController);


module.exports = router;
