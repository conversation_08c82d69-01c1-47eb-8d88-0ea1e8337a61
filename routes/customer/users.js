const  express = require('express');
const  router = express.Router();
const validateRequestPayload = require("../validation")
const userSchema = require("../validation/userSchema")
const fileUpload = require("@utils/fileUpload")

const getUserInfoController = require("@controllers/customer/users/getInfo")
const updateInfoController = require("@controllers/customer/users/updateInfo")
const updateLangController = require("@controllers/customer/users/updateLang")

const cleanOtpController = require("@controllers/customer/users/cleanOtp")
const uploadProfileController = require("@controllers/customer/users/uploadProfile")

/* GET users listing. */
router.get('/info', getUserInfoController);
router.post('/info/update', validateRequestPayload(userSchema.update) , updateInfoController);
router.post('/language/update', validateRequestPayload(userSchema.updateLanguage) , updateLangController);

router.post('/profile-picture/update',  fileUpload().fields([
        { name: 'profileFile', maxCount: 1 }
    ]), uploadProfileController );


router.post('/clean/otp' , cleanOtpController);


module.exports = router;
