const  express = require('express');
const  router = express.Router();
// const validateRequestPayload = require("./validation")
// const userSchema = require("./validation/userSchema")
 
const createTopupController = require("@controllers/customer/topup/createTopup")
const getTopupPaymentStatusController = require("@controllers/customer/topup/getTopupPaymentStatus")
const listTopupController = require("@controllers/customer/topup/listTopup")
const getTopupDetailController = require("@controllers/customer/topup/getTopupDetail")
const listTopupAmountController = require("@controllers/customer/topup/listTopupAmount")
 
router.post('/create', createTopupController);
router.get('/:id/payment-status', getTopupPaymentStatusController);
router.get('/list', listTopupController);
router.get('/:id/detail', getTopupDetailController);
router.get('/amount/list', listTopupAmountController);

 
module.exports = router;
