var express = require('express');
var router = express.Router();

const createOrderController = require("@controllers/customer/order/createOrder")
const createBulkOrderController = require("@controllers/customer/order/createBulkOrder")

const getOrderDetailController = require("@controllers/customer/order/getOrderDetail")
const getOrderDetailV2Controller = require("@controllers/customer/order/getOrderDetailV2")
const getOrderPreviewController = require("@controllers/customer/order/getOrderPreview")
const getBulkOrderPreviewController = require("@controllers/customer/order/getBulkOrderPreview")
const getBulkOrderPreviewV2Controller = require("@controllers/customer/order/getBulkOrderPreviewV2")

const listOrderDetailController = require("@controllers/customer/order/listOrder")
const listOrderV2Controller = require("@controllers/customer/order/listOrderV2")
const applyCouponController = require("@controllers/customer/order/applyCoupon")
const cancelOrderController = require("@controllers/customer/order/cancelOrder")
const cancelOrderV2Controller = require("@controllers/customer/order/cancelOrderV2")
const getCancellationStatusController = require("@controllers/customer/order/getCancellationStatus")
const getCancellationStatusV2Controller = require("@controllers/customer/order/getCancellationStatusV2");
const getOrderPaymentStatusController = require('@controllers/customer/order/getOrderPaymentStatus');
const getOrderPaymentStatusByTranIdController = require('@controllers/customer/order/getOrderPaymentStatusByTranId');
const applyCouponBulkOrderController = require('@controllers/customer/order/applyCouponBulkOrder');
const applyCouponBulkOrderV2Controller = require('@controllers/customer/order/applyCouponBulkOrderV2');

router.get('/:id/detail', getOrderDetailController );
router.get('/v2/:id/detail', getOrderDetailV2Controller );
router.get('/:id/payment-status', getOrderPaymentStatusController );
router.get('/tran/:tranId/payment-status', getOrderPaymentStatusByTranIdController );

router.get('/list', listOrderDetailController );
router.get('/v2/list', listOrderV2Controller );
router.post('/create', createOrderController );
router.post('/create/bulk', createBulkOrderController );

router.post('/preview', getOrderPreviewController );
router.post('/bulk/preview', getBulkOrderPreviewController );
router.post('/bulk/v2/preview', getBulkOrderPreviewV2Controller );

router.post('/apply/coupon', applyCouponController );
router.post('/apply/coupon/bulk-order', applyCouponBulkOrderController );
router.post('/apply/coupon/v2/bulk-order', applyCouponBulkOrderV2Controller );
router.get('/:id/cancel/status', getCancellationStatusController );
router.get('/v2/:id/cancel/status', getCancellationStatusV2Controller);
router.post('/:id/cancel', cancelOrderController );
router.post('/v2/:id/cancel', cancelOrderV2Controller );
 
 

module.exports = router;
