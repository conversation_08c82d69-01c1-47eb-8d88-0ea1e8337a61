var express = require('express');
var router = express.Router();
var usersRouter = require('./users');
var authRouter = require('./auth');
var configRouter = require('./appConfig');
var requestTicketRouter = require('./requestTicket');
var bannerRouter = require('./banner');
var addressRouter = require("./address")
var categoryRouter = require("./category")
var productRouter = require("./product")
var getHomePageController = require("@controllers/customer/getHomepage")
var paymentMethodRouter = require("./paymentMethod")
var announcementRouter = require("./announcement")
var orderRouter = require('./order')
var topupRouter = require('./topup')


router.use('/auth', authRouter);
router.use('/user', usersRouter);
router.use('/config', configRouter);
router.use('/request-ticket', requestTicketRouter);
router.use('/banner', bannerRouter);
router.use('/address', addressRouter);
router.use('/category', categoryRouter);
router.use('/payment-method', paymentMethodRouter);
router.use('/product', productRouter);
router.use('/announcement', announcementRouter);
router.use('/order', orderRouter);
router.use('/topup', topupRouter);
router.get('/homepage', getHomePageController);


module.exports = router;
