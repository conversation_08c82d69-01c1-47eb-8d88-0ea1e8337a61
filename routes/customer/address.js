const express = require('express');
const router = express.Router();
// const validateRequestPayload = require("./validation")
// const userSchema = require("./validation/userSchema")
// const userSchema = require("./validation/userSchema")
const { authUser } = require("@authUser")

const createAddressController = require("@controllers/customer/address/createAddress")
const listAddressController = require("@controllers/customer/address/listAddress")
const updateAddressController = require("@controllers/customer/address/updateAddress")
const deleteAddressController = require("@controllers/customer/address/deleteAddress")

router.get('/list', authUser, listAddressController);
router.post('/create', authUser, createAddressController);
router.post('/update', authUser, updateAddressController);
router.post('/:id/delete', authUser, deleteAddressController);

module.exports = router;
