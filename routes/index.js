var express = require('express');
var router = express.Router();
var customerRouter = require('./customer');
var adminRouter = require('./admin');
var paymentRouter = require('./payment');
var marketingRouter = require('./marketing');
const guestRouter = require('./guestRoutes');
const getHomepage = require('@controllers/customer/getHomepage');
router.use('/customer', customerRouter);
router.use('/admin', adminRouter);
router.use('/payment', paymentRouter);
router.use('/marketing', marketingRouter);
router.use('/guest', guestRouter);
router.get('/homepage', getHomepage);

module.exports = router;
