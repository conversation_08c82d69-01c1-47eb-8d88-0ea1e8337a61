const express = require('express');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;
const JWT_SECRET = process.env.JWT_SECRET;

// GET /getmode → generates temporary token
app.get('/getmode', (req, res) => {
    try {
        // Token payload
        const payload = {
            mode: 'mobile-booking',
            createdAt: Date.now()
        };

        // Create token (valid for 10 mins)
        const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '30d' });

        res.json({ success: true, token });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
});
