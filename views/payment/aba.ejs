<!DOCTYPE html>
<html lang="en">
<head>
  <title>PayWay Checkout</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
  <meta name="author" content="PayWay" />
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js"></script>

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: transparent;
      font-family: sans-serif;
    }

    .spinner-overlay {
      position: fixed;
      top: 0; left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }

    .cupertino-spinner {
      width: 28px;
      height: 28px;
      position: relative;
    }

    .cupertino-spinner div {
      display: block;
      position: absolute;
      width: 2px;
      height: 8px;
      background: #8e8e93;
      top: 0;
      left: 9px;
      transform-origin: center 10px;
      border-radius: 1px;
      animation: cupertino-spin 1s linear infinite;
    }

    .cupertino-spinner div:nth-child(1)  { transform: rotate(0deg); animation-delay: -0.9167s; }
    .cupertino-spinner div:nth-child(2)  { transform: rotate(30deg); animation-delay: -0.8333s; }
    .cupertino-spinner div:nth-child(3)  { transform: rotate(60deg); animation-delay: -0.75s; }
    .cupertino-spinner div:nth-child(4)  { transform: rotate(90deg); animation-delay: -0.6667s; }
    .cupertino-spinner div:nth-child(5)  { transform: rotate(120deg); animation-delay: -0.5833s; }
    .cupertino-spinner div:nth-child(6)  { transform: rotate(150deg); animation-delay: -0.5s; }
    .cupertino-spinner div:nth-child(7)  { transform: rotate(180deg); animation-delay: -0.4167s; }
    .cupertino-spinner div:nth-child(8)  { transform: rotate(210deg); animation-delay: -0.3333s; }
    .cupertino-spinner div:nth-child(9)  { transform: rotate(240deg); animation-delay: -0.25s; }
    .cupertino-spinner div:nth-child(10) { transform: rotate(270deg); animation-delay: -0.1667s; }
    .cupertino-spinner div:nth-child(11) { transform: rotate(300deg); animation-delay: -0.0833s; }
    .cupertino-spinner div:nth-child(12) { transform: rotate(330deg); animation-delay: 0s; }

    @keyframes cupertino-spin {
      0% { opacity: 1; }
      100% { opacity: 0.1; }
    }
  </style>
</head>

<body>
  <!-- iOS-style spinner -->
  <div class="spinner-overlay" id="loading-spinner">
    <div class="cupertino-spinner">
      <div></div><div></div><div></div><div></div><div></div><div></div>
      <div></div><div></div><div></div><div></div><div></div><div></div>
    </div>
  </div>

  <!-- Popup Checkout Form -->
  <div id="aba_main_modal" class="aba-modal" style="width:100% !important; height:100% !important; margin:0; padding:0; min-width:100% !important;">
    <div class="aba-modal-content" style="min-height:100% !important; min-width:100% !important; margin:0;">
      <form id="aba_merchant_request" method="POST" action="<%= baseUrl %>/v1/payments/purchase">
        <input type="hidden" name="hash" value="<%= hash %>" id="hash" />
        <input type="hidden" name="req_time" value="<%= reqTime %>" id="req_time" />
        <input type="hidden" name="merchant_id" value="<%= merchantId %>" id="merchant_id" />
        <input type="hidden" name="tran_id" value="<%= transactionId %>" id="tran_id" />
        <input type="hidden" name="amount" value="<%= amount %>" id="amount" />
        <input type="hidden" name="type" value="<%= type %>" id="type" />
        <input type="hidden" name="phone" value="<%= phone %>" />
        <input type="hidden" name="return_url" value="<%= returnUrl %>" />
        <input type="hidden" name="payment_option" value="<%=paymentOption%>" />
        <input type="hidden" name="continue_success_url" value="<%=successUrl%>" />
           <input type="hidden" name="return_deeplink" value="<%=returnDeeplink%>" />
        <input type="hidden" name="view_type" value="hosted_view">
      </form>
    </div>
  </div>

  <script>
    $(document).ready(function () {
      var interval = setInterval(function () {
        try {
          AbaPayway.checkout();
          clearInterval(interval);
          $('#loading-spinner').fadeOut(); // hide loader once initialized
        } catch (e) {
          console.log(e);
        }
      }, 1000);
    });
  </script>
</body>
</html>