'use strict';
const {
  Model
} = require('sequelize');

const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class Announcement extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  Announcement.init({
    
    titleEn: DataTypes.STRING,
    contentEn: DataTypes.TEXT,

    titleKm: DataTypes.STRING,
    contentKm: DataTypes.TEXT,

    titleVi: DataTypes.STRING,
    contentVi: DataTypes.TEXT,

    
    titleCn: DataTypes.STRING,
    contentCn: DataTypes.TEXT,
    
    titleTw: DataTypes.STRING,
    contentTw: DataTypes.TEXT,
    
 
    type: {
      type: DataTypes.STRING,
      defaultValue: "NOTIFY_INSTANT",
      allowNull: false,
      validate: {
        isIn: [['NOTIFY_INSTANT', 'NOTIFY_SCHEDULED']]
      },

    },
    scheduleDate: DataTypes.DATE,
    thumbnailUrl: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('thumbnailUrl');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/uploads/${rawValue}`;
      }
    },
    bannerUrlEn: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('bannerUrlEn');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/uploads/${rawValue}`;
      }
    },
    bannerUrlKm: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('bannerUrlKm');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/uploads/${rawValue}`;
      }
    },
    bannerUrlVi: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('bannerUrlVi');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/uploads/${rawValue}`;
      }
    },

    bannerUrlCn: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('bannerUrlCn');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/uploads/${rawValue}`;
      }
    },
      bannerUrlTw: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('bannerUrlTw');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/uploads/${rawValue}`;
      }
    },
  }, {
    sequelize,
    modelName: 'Announcement',
    underscored: true,
     
  });

  Announcement.prototype.toJSON = function () {
    const values = { ...this.get() };

    values.createdAt = moment(values.createdAt).format('YYYY-MM-DD hh:mm A');
    values.updatedAt = moment(values.updatedAt).format('YYYY-MM-DD hh:mm A');

    return values;
  };
  return Announcement;
};