'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class UserReferral extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      UserReferral.belongsTo(models.User, {
        as: "referee",
        foreignKey: "refereeId",
      });
      UserReferral.belongsTo(models.User, {
        as: "referrer",
        foreignKey: "referrerId",
      });

    }
  }
  UserReferral.init({
    refereeCode: DataTypes.STRING,
    referrerCode: DataTypes.STRING,
    referralCode: DataTypes.STRING,
    point: {
      type: DataTypes.DOUBLE(11, 2),
      defaultValue: 2
    },
  }, {
    sequelize,
    modelName: 'UserReferral',
    underscored: true
  });
  return UserReferral;
};