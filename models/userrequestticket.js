'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class UserRequestTicket extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      UserRequestTicket.belongsTo(models.User, {
        as: "user",
        foreignKey: "userId",
      });
    }
  }
  UserRequestTicket.init({
    type: {
      type: DataTypes.STRING,
      defaultValue: "ACCOUNT-DELETION",
      allowNull: false,
      validate: {
        isIn: [['ACCOUNT-DELETION', 'SERVICE-INQUIRY', 'REFUND', 'FEEDBACK', 'OTHERS']]
      }
    },
    subject: DataTypes.STRING,
    content: DataTypes.STRING,
    status: {
      type: DataTypes.STRING,
      defaultValue: "PENDING",
      allowNull: false,
      validate: {
        isIn: [['PENDING', 'IN-PROGRESS', 'COMPLETED', 'CANCELLED']]
      }
    },
    content: DataTypes.STRING,

  }, {
    sequelize,
    modelName: 'UserRequestTicket',
    underscored: true
  });
  return UserRequestTicket;
};