'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class Notification extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  Notification.init({
    title: DataTypes.STRING,
    content: DataTypes.STRING,
    type: {
      type: DataTypes.STRING,
      defaultValue: "NOTIFY_INSTANT",
      allowNull: false,
      validate: {
        isIn: [['NOTIFY_INSTANT', 'NOTIFY_SCHEDULED']]
      }
    },
    scheduleDate: DataTypes.DATE
  }, {
    sequelize,
    modelName: 'Notification',
    underscored: true
  });
  return Notification;
};