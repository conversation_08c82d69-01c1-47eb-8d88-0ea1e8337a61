'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class Product extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      Product.belongsTo(models.Category, {
        as: "category",
        foreignKey: "categoryId",
      });
      Product.hasMany(models.ProductOption, {
        as: "productOptions",
        foreignKey: "productId",
      });

           Product.hasMany(models.ProductOptionV2, {
        as: "productOptionV2s",
        foreignKey: "productId",
      });


      
      Product.hasMany(models.ProductAddOn, {
        as: "productAddOns",
        foreignKey: "productId",
      });

  
      // Product.hasMany(models.ProductEquipment, {
      //   as: "productEquipments",
      //   foreignKey: "productId",
      // });


    }
  }
  Product.init({
    nameEn: DataTypes.STRING,
    nameKm: DataTypes.STRING,
    nameVi: DataTypes.STRING,
    nameCn: DataTypes.STRING,
    nameTw: DataTypes.STRING,
    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    amount: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: 0
    },
    sort: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    taskInfoEn: DataTypes.JSON,
    taskInfoKm: DataTypes.JSON,
    taskInfoCn: DataTypes.JSON,
    taskInfoTw: DataTypes.JSON,
    taskInfoVi: DataTypes.JSON,
    hasExtra: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
  }, {
    sequelize,
    modelName: 'Product',
    underscored: true,
  });
  return Product;
};