'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class UserAddress extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      UserAddress.belongsTo(models.User, {
        as: "user",
        foreignKey: "userId",
      });

     UserAddress.hasOne(models.Order, {
        as: "order",
        foreignKey: "addressId",
      });

    }
  }
  UserAddress.init({
    name: DataTypes.STRING,
    address: DataTypes.STRING,
    addressDetail: DataTypes.STRING,
    floorNum: DataTypes.STRING,
    roomNum: DataTypes.STRING,
    note: DataTypes.STRING,
    sort: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    isPrimary:  {
      type: DataTypes.BOOLEAN,
      defaultValue: 1
    },
    latitude: DataTypes.DECIMAL(10, 8),  // suitable for lat/lng
    longitude: DataTypes.DECIMAL(11, 8),
    bookingDate: DataTypes.DATE,
  }, {
    sequelize,
    modelName: 'UserAddress',
    underscored: true
  });
  return UserAddress;
};