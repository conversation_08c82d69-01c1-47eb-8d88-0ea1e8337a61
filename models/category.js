"use strict";
const { Model } = require("sequelize");

const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || "http://localhost";
const moment = require("moment");

module.exports = (sequelize, DataTypes) => {
  class Category extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      Category.hasMany(models.Product, {
        as: "produacts",
        foreignKey: "categoryId",
      });

      Category.hasMany(models.ProductEquipment, {
        as: "productEquipments",
        foreignKey: "categoryId",
      });

      Category.hasMany(models.ProductAddOnV2, {
        as: "productAddOns",
        foreignKey: "categoryId",
      });
    }
  }
  Category.init(
    {
      nameEn: DataTypes.STRING,
      nameVi: DataTypes.STRING,
      nameKm: DataTypes.STRING,
      nameTw: DataTypes.STRING,
      nameCn: DataTypes.STRING,

      iconUrl: {
        type: DataTypes.STRING,
        get() {
          const rawValue = this.getDataValue("iconUrl");

          if (!rawValue) return null;
          return `${STORAGE_BASE_URL}/images/icons/${rawValue}`;
        },
      },
      status: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      isComingSoon: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      sort: {
        type: DataTypes.INTEGER,
        defaultValue: 1,
      },

      noteEn: DataTypes.STRING,
      noteKm: DataTypes.STRING,
      noteVi: DataTypes.STRING,
      noteCn: DataTypes.STRING,
      noteTw: DataTypes.STRING,

      taskInfoEn: DataTypes.JSON,
      taskInfoKm: DataTypes.JSON,
      taskInfoVi: DataTypes.JSON,
      taskInfoCn: DataTypes.JSON,
      taskInfoTw: DataTypes.JSON,
    },
    {
      sequelize,
      modelName: "Category",
      underscored: true,
    }
  );
  return Category;
};
