'use strict';
const {
  Model
} = require('sequelize');

const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class Coupon extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here

      Coupon.hasMany(models.CouponRedeem, {
        as: "couponRedeems",
        foreignKey: "couponId",
      });

    }
  }
  Coupon.init({
    name: DataTypes.STRING,
    code: DataTypes.STRING,
    promoTextEn: DataTypes.STRING,
    promoTextKm: DataTypes.STRING,
    promoTextVi: DataTypes.STRING,
    promoTextCn: DataTypes.STRING,
    promoTextTw: DataTypes.STRING,
    remark: DataTypes.STRING,
    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    value: DataTypes.DOUBLE,
    type: {
      type: DataTypes.STRING,
      defaultValue: "FIXED",
      allowNull: false,
      validate: {
        isIn: [['FIXED', 'PERCENTAGE']]
      },

    },
    effectiveDate: DataTypes.DATE,
    expiredDate: DataTypes.DATE,
    maxRedeemAmount: DataTypes.DOUBLE,
    maxRedeemPerPax: DataTypes.INTEGER,
    maxRedeemTotal: DataTypes.INTEGER,
    isNewUserOnly: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
  }, {
    sequelize,
    modelName: 'Coupon',
    underscored: true
  });
  return Coupon;
};