'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class ProductOptionV2 extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here

      ProductOptionV2.belongsTo(models.Product, {
        as: "product",
        foreignKey: "productId",
      });

     ProductOptionV2.hasMany(models.Order, {
        as: "orders",
        foreignKey: "productOptionIdV2",
      });

    }
  }
  ProductOptionV2.init({

    nameEn: DataTypes.STRING,
    nameKm: DataTypes.STRING,
    nameVi: DataTypes.STRING,
    nameCn: DataTypes.STRING,
    nameTw: DataTypes.STRING,
    
    infoEn: DataTypes.STRING,
    infoKm: DataTypes.STRING,
    infoVi: DataTypes.STRING,
    infoCn: DataTypes.STRING,
    infoTw: DataTypes.STRING,

    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    amount: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: 0
    },
    sort: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },

    floorCount: DataTypes.STRING,

    bedroomCount: DataTypes.STRING,

    hourCount: DataTypes.STRING,

    cleanerCount: DataTypes.STRING,
 
  }, {
    sequelize,
    modelName: 'ProductOptionV2',
    underscored: true
  });
  return ProductOptionV2;
};