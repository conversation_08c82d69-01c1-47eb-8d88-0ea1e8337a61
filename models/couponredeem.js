'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class CouponRedeem extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      CouponRedeem.belongsTo(models.Coupon, {
        as: "coupon",
        foreignKey: "couponId",
      });
        CouponRedeem.belongsTo(models.User, {
        as: "user",
        foreignKey: "userId",
      });
      CouponRedeem.belongsTo(models.Order, {
        as: "order",
        foreignKey: "orderId",
      });
    }
  }
  CouponRedeem.init({
    code: DataTypes.STRING,
    value: DataTypes.DOUBLE,
    type: {
      type: DataTypes.STRING,
      defaultValue: "FIXED",
      allowNull: false,
      validate: {
        isIn: [['FIXED', 'PERCENTAGE']]
      },
    },
    amount: DataTypes.DOUBLE,
  }, {
    sequelize,
    modelName: 'CouponRedeem',
    underscored: true
  });
  return CouponRedeem;
};