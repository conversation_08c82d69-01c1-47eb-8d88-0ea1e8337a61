'use strict';
const {
  Model
} = require('sequelize');

const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';

module.exports = (sequelize, DataTypes) => {
  class ProductAddOnV2 extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here

      ProductAddOnV2.belongsTo(models.Category, {
        as: "category",
        foreignKey: "categoryId",
      });


    }
  }
  ProductAddOnV2.init({
    imgUrl: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('imgUrl');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/images/add-on/${rawValue}`;
      }
    },
    nameEn: DataTypes.STRING,
    nameKm: DataTypes.STRING,
    nameVi: DataTypes.STRING,
    nameCn: DataTypes.STRING,
    nameTw: DataTypes.STRING,
    amount: DataTypes.DOUBLE(11, 2),
    duration: DataTypes.INTEGER,
    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    type: {
      type: DataTypes.STRING,
      defaultValue: "SINGLE",
      allowNull: false,
      validate: {
        isIn: [['SINGLE', 'MULTIPLE']]
      }
    },
    parentId:  DataTypes.INTEGER,
    duration:  DataTypes.INTEGER,
  }, {
    sequelize,
    modelName: 'ProductAddOnV2',
    underscored: true
  });
  return ProductAddOnV2;
};