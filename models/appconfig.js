'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class AppConfig extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  AppConfig.init({
    version: DataTypes.STRING,
    playstoreStatus: {
      type: DataTypes.STRING,
      defaultValue: "PENDING",
      allowNull: false,
      validate: {
        isIn: [['PENDING', 'RELEASED']]
      }
    },
    appstoreStatus: {
      type: DataTypes.STRING,
      defaultValue: "PENDING",
      allowNull: false,
      validate: {
        isIn: [['PENDING', 'RELEASED']]
      }
    },
    deploymentStatus: {
      type: DataTypes.STRING,
      defaultValue: "PENDING",
      allowNull: false,
      validate: {
        isIn: [['PENDING', 'RELEASED']]
      }
    },
    forceUpdate: DataTypes.BOOLEAN
  }, {
    sequelize,
    modelName: 'AppConfig',
    underscored: true,
    timestamps: false
  });

  AppConfig.prototype.toJSON = function () {
    const values = { ...this.get() };

    values.createdAt = moment(values.createdAt).format('YYYY-MM-DD hh:mm A');
    values.updatedAt = moment(values.updatedAt).format('YYYY-MM-DD hh:mm A');

    return values;
  };

  return AppConfig;
};