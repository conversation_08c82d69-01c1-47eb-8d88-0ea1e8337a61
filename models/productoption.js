'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class ProductOption extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      ProductOption.belongsTo(models.Product, {
        as: "product",
        foreignKey: "productId",
      });
      ProductOption.hasMany(models.Order, {
        as: "orders",
        foreignKey: "productOptionIdV1",
      });

    }
  }
  ProductOption.init({
    nameEn: {
      type: DataTypes.STRING,
      get() {
        const id = this.getDataValue('id');
        const rawValue = this.getDataValue('nameEn');
        if (!rawValue) return null;
        return `${rawValue}`;
      }
    },
    nameKm: DataTypes.STRING,
    nameVi: DataTypes.STRING,
    nameCn: DataTypes.STRING,
    nameTw: DataTypes.STRING,
    status: {
      type:DataTypes.BOOLEAN,
      defaultValue:1
    },
    sort: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    amount: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: 1
    },
    duration: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
 
  }, {
    sequelize,
    modelName: 'ProductOption',
    underscored: true
  });
  return ProductOption;
};