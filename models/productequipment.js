'use strict';
const {
  Model
} = require('sequelize');
const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';

module.exports = (sequelize, DataTypes) => {
  class ProductEquipment extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      // ProductEquipment.belongsTo(models.Product, {
      //   as: "product",
      //   foreignKey: "productId",
      // });


      ProductEquipment.belongsTo(models.Category, {
        as: "category",
        foreignKey: "categoryId",
      });


    }
  }
  ProductEquipment.init({
    name: DataTypes.STRING,
    imgUrl: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('imgUrl');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/images/equipments/${rawValue}`;
      }
    },
    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,

    },
    sort: {
      type: DataTypes.INTEGER,
      defaultValue: 1,

    },
  }, {
    sequelize,
    modelName: 'ProductEquipment',
    underscored: true
  });
  return ProductEquipment;
};