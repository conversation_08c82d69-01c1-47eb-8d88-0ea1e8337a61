'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';
const serviceFee = parseFloat(process.env.SERVICE_FEE)
const transportFee = parseFloat(process.env.TRANSPORT_FEE)

module.exports = (sequelize, DataTypes) => {
  class Order extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      Order.hasMany(models.OrderDetail, {
        as: "orderDetails",
        foreignKey: "orderId",
      });

      Order.hasMany(models.OrderTracking, {
        as: "orderTrackings",
        foreignKey: "orderId",
      });

      Order.belongsTo(models.User, {
        as: "user",
        foreignKey: "userId",
      });

      Order.belongsTo(models.ProductOption, {
        as: "productOption",
        foreignKey: "productOptionIdV1",
      });

      Order.belongsTo(models.ProductOptionV2, {
        as: "productOptionV2",
        foreignKey: "productOptionIdV2",
      });

      Order.hasOne(models.CouponRedeem, {
        as: "couponRdeem",
        foreignKey: "orderId",
      });

      Order.belongsTo(models.UserAddress, {
        as: "userAddress",
        foreignKey: "addressId",
      });

    }
  }

  Order.init({
    bulkOrderId: {
      type: DataTypes.STRING(10),
      allowNull: false,
      unique: true,
    },
    thumbnailUrl: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('thumbnailUrl');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/images/icons/${rawValue}`;
      }
    },
    isPrimary: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    customerFirstName: {
      type: DataTypes.STRING,
    },
    customerLastName: {
      type: DataTypes.STRING,
    },
    customerEmail: {
      type: DataTypes.STRING,
    },
    customerPhone: {
      type: DataTypes.STRING,
    },
    customerPhoneDisplay: {
      type: DataTypes.VIRTUAL,
      get() {
        const phone = this.getDataValue('customerPhone');
        if (!phone || phone.length < 9) return phone;

        // Example: 85586585891
        const countryCode = phone.slice(0, 3);
        const remaining = phone.slice(3);

        let formatted = '';
        if (remaining.length === 8) {
          formatted = `${remaining.slice(0, 2)}-${remaining.slice(2, 5)}-${remaining.slice(5)}`;
        } else if (remaining.length === 7) {
          formatted = `${remaining.slice(0, 3)}-${remaining.slice(3, 5)}-${remaining.slice(5)}`;
        } else {
          formatted = remaining;
        }

        return `(${countryCode}) ${formatted}`;
      }
    },
    serviceDuration: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    paymentMethodDisplay: {
      type: DataTypes.STRING,
    },
    paymentMethodType: {
      type: DataTypes.STRING,
      defaultValue: "CASH",
      allowNull: false,
      validate: {
        isIn: [['ONLINE', 'BANK_TRANSFER', 'CASH', 'POINT']]
      }
    },
    paymentMethod: {
      type: DataTypes.STRING,
    },
    tranId: DataTypes.STRING,
    tranInitDate: DataTypes.DATE,

    paymentStatus: {
      type: DataTypes.STRING,
      defaultValue: "PENDING",
      allowNull: false,
      validate: {
        isIn: [['PENDING', 'IN-REVIEW', 'FAILED', 'PAID', 'REFUNDED']]
      }
    },
    receiptFile: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('receiptFile');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/uploads/receipts/${rawValue}`;
      }
    },
    status: {
      type: DataTypes.STRING,
      defaultValue: "PENDING",
      allowNull: false,
      validate: {
        isIn: [['PENDING', 'ACCEPTED', 'IN-PROGRESS', 'COMPLETED', 'CANCELLED', 'REJECTED']]
      }
    },
    couponCode: {
      type: DataTypes.STRING,
    },
    discount: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: 0
    },
    serviceFee: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: serviceFee
    },
    transportFee: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: transportFee
    },
    amount: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: 0
    },
    amountAddOn: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: 0
    },
    vat: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: 0.10
    },

    totalAmount: {
      type: DataTypes.VIRTUAL,
      get() {
        return (this.amount - this.discount + (this.serviceFee + this.transportFee)).toFixed(2) * 1;
      },
    },
    vatFee: {
      type: DataTypes.VIRTUAL,
      get() {

        const total = this.amount - this.discount + (this.serviceFee + this.transportFee);

        return (total * this.vat).toFixed(2) * 1;
      },
    },
    totalPayableAmount: {
      type: DataTypes.VIRTUAL,
      get() {
        const total = this.amount - this.discount + (this.serviceFee + this.transportFee);
        const vatFee = (total * this.vat);
        return (total + vatFee).toFixed(2) * 1;
      },
    },
    address: DataTypes.TEXT,
    floorNum: DataTypes.STRING,
    roomNum: DataTypes.STRING,
    scheduleStartDate: DataTypes.DATE,
    serviceDuration: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    startDate: {
      type: DataTypes.VIRTUAL,
      get() {
        return moment(this.getDataValue('scheduleStartDate')).format('DD-MM-YYYY');
      },
    },
    startTime: {
      type: DataTypes.VIRTUAL,
      get() {
        return moment(this.getDataValue('scheduleStartDate')).format('h:mm A');
      },
    },
    endTime: {
      type: DataTypes.VIRTUAL,
      get() {
        const start = this.getDataValue('scheduleStartDate');
        const duration = this.getDataValue('serviceDuration') || 1;
        return moment(start).add(duration, 'hours').format('h:mm A');
      },
    },

    note: DataTypes.STRING,

    cleanerCount: DataTypes.STRING,
    hourCount: DataTypes.STRING,
    bedroomCount: DataTypes.STRING,
    floorCount: DataTypes.STRING,

  }, {
    sequelize,
    modelName: 'Order',
    underscored: true,
  });

  // 👇 Add this block
  Order.prototype.toJSON = function () {
    const values = Object.assign({}, this.get());

    if (values.amount != null) {
      values.amount = `$${values.amount.toFixed(2)}`;
    }
    if (values.discount != null) {
      values.discount = `$${values.discount.toFixed(2)}`;
    }
    if (values.serviceFee != null) {
      values.serviceFee = `$${values.serviceFee.toFixed(2)}`;
    }

    if (values.transportFee != null) {
      values.transportFee = `$${values.transportFee.toFixed(2)}`;
    }

    if (values.totalAmount != null) {
      values.totalAmount = `$${values.totalAmount.toFixed(2)}`;
    }

    if (values.vatFee != null) {
      values.vatFee = `$${values.vatFee.toFixed(2)}`;
    }

    if (values.totalPayableAmount != null) {
      values.totalPayableAmount = `$${values.totalPayableAmount.toFixed(2)}`;
    }

    values.createdAt = moment(values.createdAt).format('YYYY-MM-DD hh:mm A');
    values.updatedAt = moment(values.updatedAt).format('YYYY-MM-DD hh:mm A');

    return values;
  };



  return Order;
};