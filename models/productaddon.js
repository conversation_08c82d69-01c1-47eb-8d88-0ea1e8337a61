'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class ProductAddOn extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      ProductAddOn.belongsTo(models.Product, {
        as: "product",
        foreignKey: "productId",
      });
      ProductAddOn.hasMany(models.OrderDetail, {
        as: "orderDetails",
        foreignKey: "productAddOnId",
      });
    }
  }
  ProductAddOn.init({
    nameEn: {
      type: DataTypes.STRING,
      get() {
        const id = this.getDataValue('id');
        const rawValue = this.getDataValue('nameEn');
        if (!rawValue) return null;
        return `${rawValue}`;
      }
    },
    nameKm: DataTypes.STRING,
    nameCn: DataTypes.STRING,
    nameTw: DataTypes.STRING,
    nameVi: DataTypes.STRING,
    category: DataTypes.STRING,
    amount: DataTypes.DOUBLE(10, 2),
    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    allowQuantity: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    sort: {
      type: DataTypes.INTEGER,
      defaultValue: true
    },
    duration: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
  }, {
    sequelize,
    modelName: 'ProductAddOn',
    underscored: true
  });
  return ProductAddOn;
};