'use strict';
const {
  Model
} = require('sequelize');

const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';

module.exports = (sequelize, DataTypes) => {
  class SystemConfig extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  SystemConfig.init({
    nameEn: DataTypes.STRING,
    nameKm: DataTypes.STRING,
    nameVi: DataTypes.STRING,
    nameCn: DataTypes.STRING,
    nameTw: DataTypes.STRING,
     

    code: DataTypes.STRING,
    imgUrlActive: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('imgUrlActive');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/images/icons/systemConfig/${rawValue}`;
      }
    },

    imgUrlInactive: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('imgUrlInactive');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/images/icons/systemConfig/${rawValue}`;
      }
    },
    category: DataTypes.STRING,
    remarkEn: DataTypes.STRING,
    remarkKm: DataTypes.STRING,
    remarkCn: DataTypes.STRING,
    remarkTw: DataTypes.STRING,
    remarkVi: DataTypes.STRING,

  }, {
    sequelize,
    modelName: 'SystemConfig',
    underscored: true,
    timestamps: false
  });
  return SystemConfig;
};