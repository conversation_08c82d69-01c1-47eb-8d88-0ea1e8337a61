'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class OrderTracking extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      OrderTracking.belongsTo(models.Order, {
        as: "order",
        foreignKey: "orderId",
      });
    }
  }
  OrderTracking.init({
    status: {
      type: DataTypes.STRING,
      defaultValue: "PENDING",
      allowNull: false,
      validate: {
        isIn: [['PENDING', 'ACCEPTED', 'IN-PROGRESS', 'COMPLETED', 'CANCELLED']]
      }
    },
    remarkEn: DataTypes.STRING,
    remarkKh: DataTypes.STRING,
    remarkZh: DataTypes.STRING

  }, {
    sequelize,
    modelName: 'OrderTracking',
    underscored: true,
  });
  return OrderTracking;
};