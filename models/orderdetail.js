'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class OrderDetail extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      OrderDetail.belongsTo(models.Order, {
        as: "order",
        foreignKey: "orderId",
      });
      OrderDetail.belongsTo(models.ProductAddOn, {
        as: "productAddOn",
        foreignKey: "productAddOnId",
      });

    }
  }
  
  OrderDetail.init({
    nameEn: DataTypes.STRING, 
    nameKm: DataTypes.STRING,
    nameVi: DataTypes.STRING,
    nameCn: DataTypes.STRING,
    nameTw: DataTypes.STRING,
     
    amount: {
      type: DataTypes.DOUBLE(10, 2),
      defaultValue: 0
    },
    qty: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    }
  }, {
    sequelize,
    modelName: 'OrderDetail',
    underscored: true,
  });
  return OrderDetail;
};