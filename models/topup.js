'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');
const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';

module.exports = (sequelize, DataTypes) => {
  class Topup extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here

      Topup.belongsTo(models.User, {
        as: "user",
        foreignKey: "userId",
      });

    }
  }
  Topup.init({
    tranId: DataTypes.STRING,
    tranInitDate: DataTypes.DATE,
    amount: DataTypes.DOUBLE(11, 2),
    credit: DataTypes.DOUBLE(11, 2),
    balance: {
      type: DataTypes.DOUBLE(11, 2),
      defaultValue: 0.00
    },
    status: {
      type: DataTypes.STRING,
      defaultValue: "PENDING",
      allowNull: false,
      validate: {
        isIn: [['PENDING', 'IN-REVIEW', 'FAILED', 'PAID']]
      }
    },
    remarkEn: DataTypes.STRING,
    remarkKm: DataTypes.STRING,
    remarkVi: DataTypes.STRING,
    remarkCn: DataTypes.STRING,
    remarkTw: DataTypes.STRING,
    paymentMethod: DataTypes.STRING,
    paymentMethodDisplay: {
      type: DataTypes.VIRTUAL,
      get() {
        const paymentMethod = this.getDataValue('paymentMethod');
        return `${paymentMethod}`;
      }
    },
    expDate: DataTypes.DATEONLY,
    receiptFile: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('receiptFile');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/uploads/receipts/${rawValue}`;
      }
    },
  }, {
    sequelize,
    modelName: 'Topup',
    underscored: true,
  });

  // 👇 Add this block
  Topup.prototype.toJSON = function () {
    const values = Object.assign({}, this.get());


    if (values.amount != null) {
      values.amount = `$${values.amount.toFixed(2)}`;
    }

    if (values.credit != null) {
      values.credit = `${values.credit.toFixed(2)} POINT`;
    }

    if (values.balance != null) {
      values.balance = `$${values.balance.toFixed(2)}`;
    }

    values.tranInitDate = moment(values.tranInitDate).format('YYYY-MM-DD hh:mm A');
    values.createdAt = moment(values.createdAt).format('YYYY-MM-DD hh:mm A');
    values.updatedAt = moment(values.updatedAt).format('YYYY-MM-DD hh:mm A');

    return values;
  };

  return Topup;
};