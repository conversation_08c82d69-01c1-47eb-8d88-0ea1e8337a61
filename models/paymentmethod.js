'use strict';
const {
  Model
} = require('sequelize');

const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class PaymentMethod extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  PaymentMethod.init({
    nameEn: DataTypes.STRING,
    nameKm: DataTypes.STRING,
    nameVi: DataTypes.STRING,
    nameCn: DataTypes.STRING,
    nameTw: DataTypes.STRING,
     

    contentEn: DataTypes.STRING,
    contentKm: DataTypes.STRING,
    contentCn: DataTypes.STRING,
    contentTw: DataTypes.STRING,
    contentVi: DataTypes.STRING,

    code: DataTypes.STRING, 
    type: DataTypes.STRING,
    logoUrl: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('logoUrl');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/images/icons/payment/${rawValue}`;
      }
    },
    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    sort: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    remark: DataTypes.STRING,
  }, {
    sequelize,
    modelName: 'PaymentMethod',
    underscored: true,
  });
  return PaymentMethod;
};