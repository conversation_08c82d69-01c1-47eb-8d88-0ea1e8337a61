'use strict';
const {
  Model
} = require('sequelize');
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class UserOtp extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  UserOtp.init({
    username: DataTypes.STRING,
    otp: DataTypes.STRING,
    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
     isVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    expDate: {
      type: DataTypes.DATE,
      defaultValue: () => new Date(Date.now() + 5 * 60 * 1000) // now + 5 minutes
        //  defaultValue: () => new Date(Date.now() + 10 * 1 * 1000) 
    },
    deviceId: DataTypes.STRING,
    clientIp: DataTypes.STRING,
  }, {
    sequelize,
    modelName: 'UserOtp',
    underscored: true
  });
  return UserOtp;
};