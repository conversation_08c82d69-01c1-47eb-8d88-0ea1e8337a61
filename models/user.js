'use strict';
const {
  Model
} = require('sequelize');
const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';
const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  class User extends Model {
    static associate(models) {
      User.hasMany(models.UserToken, {
        as: "userTokens",
        foreignKey: "userId",
      });
      User.hasMany(models.Topup, {
        as: "topups",
        foreignKey: "userId",
      });
      User.hasMany(models.UserRequestTicket, {
        as: "userRequestTickets",
        foreignKey: "userId",
      });
      User.hasMany(models.UserAddress, {
        as: "userAddresses",
        foreignKey: "userId",
      });
      User.hasMany(models.Order, {
        as: "orders",
        foreignKey: "userId",
      });
      User.hasOne(models.UserReferral, {
        as: "userReferee",
        foreignKey: "refereeId",
      });
      User.hasMany(models.UserReferral, {
        as: "userReferrers",
        foreignKey: "referrerId",
      });
      User.hasMany(models.CouponRedeem, {
        as: "couponRedeems",
        foreignKey: "userId",
      });
    }
  }
  User.init({
    profileUrl: {
      type: DataTypes.STRING,
      get() {
        const rawValue = this.getDataValue('profileUrl');
        if (!rawValue) return null;
        return `${STORAGE_BASE_URL}/uploads/${rawValue}`;
      }
    },
    username: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false
    },
    password: {
      type: DataTypes.STRING,
      allowNull: true, // Allow null for GUSTMODE users
      validate: {
        notEmpty: {
          msg: 'Password is required',
          if: function(user) {
            return user.type !== 'GUSTMODE';
          }
        }
      }
    },
    firstName: DataTypes.STRING,
    lastName: DataTypes.STRING,
    balance: {
      type: DataTypes.DOUBLE(11, 2),
      defaultValue: 5.00
    },
    language: {
      type: DataTypes.STRING,
      defaultValue: "en",
      allowNull: false,
      set(value) {
        this.setDataValue('language', value.toLowerCase());
      },
      validate: {
        isIn: [['en', 'km', 'zh-cn', 'zh-tw', 'vi']]
      }
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true, // Allow null for GUSTMODE users
      validate: {
        isEmail: {
          msg: 'Please provide a valid email address',
          if: function(user) {
            return user.type !== 'GUSTMODE';
          }
        }
      }
    },
    status: {
      type: DataTypes.STRING,
      defaultValue: "PENDING",
      allowNull: false,
      validate: {
        isIn: [['PENDING', 'ACTIVE', 'SUSPENDED', 'DELETED']]
      }
    },
    type: {
      type: DataTypes.STRING,
      defaultValue: "USER",
      allowNull: false,
      validate: {
        isIn: [['USER', 'ADMIN', 'GUSTMODE']]
      }
    },
    referrerCode: DataTypes.STRING,
    referralCode: {
      type: DataTypes.VIRTUAL,
      get() {
        const rawUsername = typeof this.username === 'string' ? this.username : '';
        const cleaned = rawUsername.replace(/\D/g, '');
        const referralCode = cleaned.replace(/^855/, '0');
        return referralCode;
      },
    },
  }, {
    sequelize,
    modelName: 'User',
    underscored: true,
    hooks: {
      beforeCreate: (user) => {
        if (user.type === 'GUSTMODE') {
          user.username = user.username || `guest_${Date.now()}`;
          user.status = 'ACTIVE';
          user.password = null;
          user.email = null;
          user.balance = 0.00;
        }
      },
      beforeUpdate: (user) => {
        if (user.type === 'GUSTMODE' && user._previousDataValues.type !== 'GUSTMODE') {
          throw new Error('Cannot convert existing users to guest mode');
        }
      }
    }
  });
  return User;
};