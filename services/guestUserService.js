const { User } = require('@db');

class GuestUserService {
  static async createGuestUser() {
    try {
      const guestUser = await User.create({
        type: 'GUSTMODE',
        status: 'ACTIVE'
      });

      return {
        success: true,
        data: guestUser
      };
    } catch (error) {
      throw new Error(`Failed to create guest user: ${error.message}`);
    }
  }

  static async convertToRegularUser(guestUserId, userData) {
    try {
      const guestUser = await User.findByPk(guestUserId);
      
      if (!guestUser || guestUser.type !== 'GUSTMODE') {
        throw new Error('User is not a guest user');
      }

      // Validate required fields
      if (!userData.username || !userData.password || !userData.email) {
        throw new Error('Username, password, and email are required');
      }

      // Update user to regular user
      const updatedUser = await guestUser.update({
        ...userData,
        type: 'USER'
      });

      return {
        success: true,
        data: updatedUser
      };
    } catch (error) {
      throw new Error(`Failed to convert guest user: ${error.message}`);
    }
  }

  static async deleteGuestUser(guestUserId) {
    try {
      const guestUser = await User.findByPk(guestUserId);
      
      if (!guestUser || guestUser.type !== 'GUSTMODE') {
        throw new Error('User is not a guest user');
      }

      await guestUser.destroy();

      return {
        success: true,
        message: 'Guest user deleted successfully'
      };
    } catch (error) {
      throw new Error(`Failed to delete guest user: ${error.message}`);
    }
  }
}

module.exports = GuestUserService;