const db = require("@db")
const { Op } = require("sequelize")
const jwt = require('jsonwebtoken');
const alertOrderToAdmin = require("@services/telegram/alertOrderToAdmin")
const moment = require('moment');

const serviceFee = parseFloat(process.env.SERVICE_FEE)
const transportFee = parseFloat(process.env.TRANSPORT_FEE)

module.exports = async (productOptionId, productAddOnIds, scheduleStartDate, language) => {


    const productOption = await db.ProductOptionV2.findOne({
        where: { id: productOptionId },
        attributes: ["id", "amount", "nameEn", "nameKm", "nameVi", "nameCn", "nameTw", "nameVi", "amount", 'duration'],
        include: [
            {
                model: db.Product, as: "product",
                attributes: ["nameEn", "nameKm", "nameCn", "nameTw", "nameVi"],
                include: [
                    {
                        model: db.Category, as: "category",
                        attributes: ["nameEn", "nameKm", "nameVi", "nameCn", "nameTw", "iconUrl"],
                    }
                ]
            },

        ],

    })


    let addOnIds = productAddOnIds.map(p=>p.id);

    const productAddOn = await db.ProductAddOnV2.findAll({
        attributes: ["id", "nameEn", "nameKm", "nameVi", "nameCn", "nameTw", "amount"],
        where: {
            id: {
                [Op.in]: addOnIds
            }
        },
        raw: true
    })


    let productAddOns = productAddOn.map(p => {
        let proAddOn = 0;
             
        for (let pro of productAddOnIds) {
 
            if (p.id == pro.id) {
                proAddOn = pro.qty
            }
        }
        return { ...p, qty: proAddOn }
    })
    
    const addOnAmount = productAddOn.reduce((sum, addOn) => {
        let amount = 0;
        for (let p of productAddOnIds) {
            if (p.id == addOn.id) {
                amount = amount + (addOn.amount * p.qty);
            }
        }

        return sum + amount

    }, 0);

 

    const discount = 0

    const amount = productOption.amount + addOnAmount
    const totalAmount = amount + serviceFee + transportFee
    const vatFee = totalAmount * 0.1
    const totalPayableAmount = totalAmount + vatFee

    let hourLabel;

 

    switch (language) {
        case 'KH':
            hourLabel = 'ម៉ោង'; // Khmer
            break;
        case 'ZH':
            hourLabel = '小时'; // Chinese
            break;
        case 'EN':

        default:
            hourLabel = 'hour'; // Default to English
    }

    const orderPayload = {
        productAddOn: productAddOns,
        productOption,

        startDate: `${moment(scheduleStartDate).format('DD-MM-YYYY')} `,
        startTime: `${moment(scheduleStartDate).format('h:mm A')} `,
        endTime: `${moment(scheduleStartDate).add(productOption.duration, 'hours').format('h:mm A')} `,
        duration: `${productOption.duration} ${hourLabel} `,

        amount: amount,
        amountDisplay: `$ ${amount.toFixed(2)}`,

        addOnAmount,
        addOnAmountDisplay: `$ ${addOnAmount.toFixed(2)}`,

        discount: discount,
        discountDisplay: `$ ${discount.toFixed(2)}`,

        totalAmount: totalAmount,
        totalAmountDisplay: `$ ${totalAmount.toFixed(2)}`,

        serviceFee: serviceFee,
        serviceFeeDisplay: `$ ${serviceFee.toFixed(2)}`,

        transportFee: transportFee,
        transportFeeDisplay: `$ ${transportFee.toFixed(2)}`,

        vatFee: parseFloat(vatFee.toFixed(2)),
        vatFeeDisplay: `$ ${vatFee.toFixed(2)}`,

        totalPayableAmount: parseFloat(totalPayableAmount.toFixed(2)),
        totalPayableAmountDisplay: `$ ${totalPayableAmount.toFixed(2)}`,
    }

    return orderPayload
}
