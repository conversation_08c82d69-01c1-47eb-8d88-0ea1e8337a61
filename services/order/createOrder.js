const db = require("@db")
const { Op } = require("sequelize")

const checkoutPreauthService = require("@services/payway/checkoutPreauth")

module.exports = async (bulkOrderId,
    userTb,
    productOptionIdV2,
    productAddOnIds,
    serviceFee,
    transportFee,
    couponCode,
    paymentMethod,
    address
) => {

    const productOption = await db.ProductOptionV2.findOne({
        where: { id: productOptionIdV2 },
        attributes: ["id", "amount", "hourCount", "cleanerCount", "bedroomCount", "floorCount"],
        include: [
            {
                model: db.Product,
                as: 'product',
                attributes: ["id"],
                include: [
                    {
                        as: 'category',
                        model: db.Category,
                        attributes: ["id", "iconUrl"],
                    }
                ]
            }
        ],
        raw: true
    })


    if (productOption.code == "cash") {
        return { error: "This payment option is not supported" }
    }

    const productAddOn = await db.ProductAddOnV2.findAll({
        where: {
            id: {
                [Op.in]: productAddOnIds.map(p => p.id)
            }
        },
        raw: true
    })

    let orderDetailPayload = productAddOn.map(pro => {
        let productAddOn = productAddOnIds.find((p) => {
            if (p.id == pro.id) {
                return p
            }
        })

        return {
            productAddOnId: productAddOn.id,
            nameEn: pro.nameEn,
            nameKm: pro.nameKm,
            nameVi: pro.nameVi,
            nameCn: pro.nameCn,
            nameTw: pro.nameTw,
            amount: pro.amount,
            qty: productAddOn.qty
        }
    })
    
  

    const addOnAmount = orderDetailPayload.reduce((sum, addOn) => sum + (addOn.amount * addOn.qty || 0), 0);
    console.log(" addOnAmount = "  , addOnAmount )

    // const amount = productOption.amount + addOnAmount
    // const discount = couponTb.type == "FIXED" ? couponTb.value  : amount * couponTb.value
    // const amountAfterDiscount =  amount - discount
    // const vatFee = (amountAfterDiscount + serviceFee) * 0.1
    // const totalPayableAmount = amountAfterDiscount + serviceFee + vatFee

    let orderPayload = {
        userId: userTb.id,
        customerFirstName: userTb.firstName,
        customerLastName: userTb.lastName,
        customerPhone: userTb.username,
        customerEmail: userTb.email,
        hourCount: productOption.hourCount,
        cleanerCount: productOption.cleanerCount,
        bedroomCount: productOption.bedroomCount,
        floorCount: productOption.floorCount,
        thumbnailUrl: productOption['product.category.iconUrl'],
        addressId: address.addressId,
        address: address.address,
        floorNum: address.floorNum,
        roomNum: address.roomNum,
        note: address.note,
        scheduleStartDate: address.scheduleStartDate,
        couponCode,
        amount: productOption.amount + addOnAmount,
        amountAddOn: addOnAmount,
        serviceDuration: productOption.duration,
        serviceFee,
        transportFee
    }

    const customerAddress = await db.UserAddress.findOne({ where: { id: orderPayload.addressId || 0, userId: userTb.id } });

    if (customerAddress) {
        orderPayload = {
            ...orderPayload,
            address: customerAddress.address,
            floorNum: customerAddress.floorNum,
            roomNum: customerAddress.roomNum,
        }
    }

    if (couponCode) {
        const couponTb = await db.Coupon.findOne({ where: { code: couponCode } })

        if (!couponTb) {

            return { error: "This coupon is invalid" }
        }
        if (couponTb.status == false) {

            return { error: "This coupon is not active yet" };
        }
        const now = new Date();
        if (couponTb.effectiveDate && now < new Date(couponTb.effectiveDate)) {

            return { error: "This coupon has expired" };
        }

        if (couponTb.expiredDate && now > new Date(couponTb.expiredDate)) {
            return { error: "This coupon has expired" }
        }

        const amount = productOption.amount + addOnAmount


        const discount = couponTb.type == "FIXED" ? couponTb.value : amount * couponTb.value
        orderPayload = { ...orderPayload, discount }
    }

    const paymentMethodTb = await db.PaymentMethod.findOne({ where: { code: paymentMethod } })

    if (!paymentMethodTb) return { error: "Payment method not found!" }

    orderPayload = {
        ...orderPayload,
        bulkOrderId,
        productOptionIdV2,
        paymentMethodType: paymentMethodTb.type,
        paymentMethodDisplay: paymentMethodTb.nameEn,
        paymentStatus: paymentMethodTb.code == "ababank_transfer" && receiptFile != null ? "IN-REVIEW" : "PENDING"
    }

    const orderTb = await db.Order.create(orderPayload)

    orderDetailPayload = orderDetailPayload.map(o => {
        return { ...o, orderId: orderTb.id }
    })

    await db.OrderDetail.bulkCreate(orderDetailPayload);

    const orderDetailTb = await db.Order.findOne({
        attributes: { exclude: ["userId", "productOptionIdV2", "updatedAt"] },
        where: { userId: userTb.id, id: orderTb.id },
        include: [
            {
                model: db.ProductOptionV2, as: 'productOptionV2',
                attributes: { exclude: ["createdAt", "updatedAt", "sort", "productId"] },
                include: [
                    {
                        model: db.Product, as: 'product',
                        attributes: { exclude: ["createdAt", "updatedAt", "categoryId", "sort", "status", "amount"] },
                        include: [
                            {
                                model: db.Category, as: 'category',
                                attributes: { exclude: ["createdAt", "updatedAt", "sort", "status"] },

                            }

                        ]
                    }

                ]
            },
            {
                model: db.OrderDetail, as: 'orderDetails',
                attributes: { exclude: ["qty", "orderId", "createdAt", "updatedAt"] },

            },
            {
                model: db.OrderTracking,
                as: 'orderTrackings',
                attributes: { exclude: ["updatedAt", "createdAt"] }
            },
        ]
    })


    let hourLabel;
    let languageLabel;

    if (orderTb.paymentMethod == "beasy_point") {
        if (userTb.balance < orderTb.totalAmount) {
            return { error: "Your bEasy point is insufficient!" }
        } else {
            const balance = userTb.balance - (orderTb.totalAmount + orderTb.vatFee)
            await db.User.update({ balance }, { where: { id: userTb.id } })
            orderTb.paymentStatus = "PAID";
            await orderTb.save();
        }
    }

    switch (userTb.language) {
        case 'KH':
            hourLabel = 'ម៉ោង'; // Khmer
            languageLabel = 'KHMER Language';
            break;
        case 'ZH':
            hourLabel = '小时'; // Chinese
            languageLabel = 'CHINESE Language';
            break;
        case 'EN':
        default:
            hourLabel = 'hour'; // Default to English
            languageLabel = 'ENGLISH Language';
    }

    const addOnList = productAddOn
        .map((addOn, i) => `#${i + 1}-${addOn.nameEn}`)
        .join(' , ');


    orderDetailTb.dataValues.duration = `${orderDetailTb?.productOption?.duration || "N/A"} ${hourLabel}`
    // let paymentResp = await checkoutRestService(orderDetailTb.id, orderDetailTb.paymentMethod)
    let paymentResp = await checkoutPreauthService(orderDetailTb.id, orderDetailTb.paymentMethod)

    switch (paymentMethod) {

        case "cards":
            return {
                ...orderDetailTb.dataValues,
                receiptFile: orderDetailTb.receiptFile,
                paymentResp: { ...paymentResp, checkoutUrl: paymentResp.checkoutUrl },
            }

        case "ababank_transfef":
            return {
                ...orderDetailTb.dataValues,
                receiptFile: orderDetailTb.receiptFile,
                paymentResp: { ...paymentResp, checkoutUrl: paymentResp.checkout_qr_url },
            }

        case "beasy_point":
            return {
                ...orderDetailTb.dataValues,
                receiptFile: orderDetailTb.receiptFile,
                paymentResp: {
                    currentBalance: `${userTb.balance.toFixed(2)}`,
                    deductCredit: `${(orderTb.totalAmount + orderTb.vatFee).toFixed(2)}`
                }
            }

        default:
            return {
                ...orderDetailTb.dataValues,
                receiptFile: orderDetailTb.receiptFile,
                paymentResp: { ...paymentResp, checkoutUrl: paymentResp.checkout_qr_url },
            }

    }

}
