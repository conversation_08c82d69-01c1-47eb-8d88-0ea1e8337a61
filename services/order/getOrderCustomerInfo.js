
const db = require("@db")

module.exports = async (userId, userAddressId) => {
    
    const userTb = await db.User.findOne({ where: { id: userId } })

    let userAddressTb;

    if (userAddressId) {
        userAddressTb = await db.UserAddress.findOne({
            where: { id: userAddressId },
            attributes:
                [
                    "name",
                    "address",
                    "addressDetail",
                    "floorNum",
                    "roomNum",
                    "note"
                ]
        })
    }

    

    const custInfo = {
        address: userAddressTb?.dataValues.address,
        floorNum: userAddressTb?.dataValues.floorNum,
        roomNum: userAddressTb?.dataValues.roomNum,
        userId,
        customerFirstName: userTb.firstName,
        customerLastName: userTb.lastName,
        customerPhone: userTb.username,
        customerEmail: userTb.email,
    }

    return custInfo

}



