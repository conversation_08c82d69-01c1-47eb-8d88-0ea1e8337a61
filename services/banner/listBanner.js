 
const db = require("@db")

const listBanner = async (query, attributes) => {

    const { type , status} = query

    let sqlStr = { }

    if (type != undefined) {
        sqlStr = { ...sqlStr, type }
    }
        if (status != undefined) {
        sqlStr = { ...sqlStr, status }
    }
 
    const resp = await db.Banner.findAll({
        where: sqlStr,
        order: [["sort", "DESC"]],
        ...(attributes && attributes.length > 0 && { attributes })
    })

    return resp

}

module.exports = listBanner