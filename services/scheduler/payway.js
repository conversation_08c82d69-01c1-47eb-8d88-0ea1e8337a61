const cron = require('node-cron');
const db = require('@db');
const checkPaymentStatusApi = require("@apis/payway/checkPaymentStatusApi");
const { Op } = require("sequelize")

const merchantId = process.env.ABA_MERCHANT_ID
const merchantApiKey = process.env.ABA_API_KEY




cron.schedule('*/10 * * * * *', async () => {

   const threeMinutesAgo = new Date(Date.now() - 3 * 60 * 1000); // 3 minutes in milliseconds

   const orders = await db.Order.findAll({
      attributes: ["bulkOrderId", "tranId"],
      where: {
         tranInitDate: {
            [Op.gte]: threeMinutesAgo
         },
         paymentStatus: "PENDING",
         paymentMethodType: "ONLINE"
      },
      group: ["bulkOrderId", "tranId"],  // 👈 GROUP BY clause here
      raw: true
   });

   for (const order of orders) {

      let payment = await checkPaymentStatusApi(merchantId, merchantApiKey, order.tranId);

      if (payment.status.code == 0) {
         if (payment.data.payment_status == "APPROVED") {
            await db.Order.update({ paymentStatus: "PAID" }, { where: { bulkOrderId: order.bulkOrderId } })
         }
      }

   }

});


