 
const db = require("@db")

const listCategory = async (query, attributes) => {

    const { type , status} = query

    let sqlStr = { }

    if (type != undefined) {
        sqlStr = { ...sqlStr, type }
    }
        if (status != undefined) {
        sqlStr = { ...sqlStr, status }
    }
 
    const resp = await db.Category.findAll({
        where: sqlStr,
        order: [["sort", "ASC"]],
        ...(attributes && attributes.length > 0 && { attributes })
    })

    return resp

}

module.exports = listCategory