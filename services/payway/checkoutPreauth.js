const moment = require("moment");
const db = require("@db");

const hostUrl = process.env.HOST_URL
const generateHash = require("@services/payway/utils/generateHash")
const getPaywayConfig = require("@services/payway/utils/getPaywayConfig")
const initiatePaywayCheckout = require("@apis/payway/initiatePaywayCheckout")

const merchantId = process.env.ABA_MERCHANT_ID;
const apiKey = process.env.ABA_API_KEY;

const baseUrl = process.env.ABA_BASE_URL;

const checkoutPreauth = async (orderId, paymentMethod) => {

    const {
        cancelUrl,
        returnDeeplink,
        returnUrl,
        successUrl,
        reqTime,
        abaScript,
        transactionId
    } = getPaywayConfig('/api/payment/aba/callback/success')

    const type = "pre-auth"

    const orderTb = await db.Order.findOne({
        where: { id: orderId },
    });

    console.log(" Payment - orderTb = ", orderTb.amount)

    if (orderTb == null) {
        return { errorMessage: "Order ID not found", stack: "Can't find order with ID = " + orderId };
    }

    const paymentMethodTb = await db.PaymentMethod.findOne({
        where: {
            code: paymentMethod,
            status: 1
        },
    });

    if (paymentMethodTb == null) {
        return { errorMessage: "Payment method not found", stack: "Can't find payment method with CODE = " + paymentMethod, };
    }

    let hash = generateHash(
        apiKey,
        [
            reqTime,
            merchantId,
            transactionId,
            orderTb.totalPayableAmount,
            orderTb.customerPhone,
            type,
            paymentMethod,
            returnUrl,
            successUrl,
            returnDeeplink
        ]
    );


    const paymentData = {
        id: orderTb.id,
        baseUrl,
        reqTime,
        merchantId,
        transactionId,
        phone: orderTb.customerPhone,
        amount: orderTb.totalPayableAmount,
        paymentOption: paymentMethod,
        type,
        returnUrl,
        hash,
        successUrl,
        abaScript,
        returnDeeplink,
        checkoutUrl: `${hostUrl}/api/payment/aba/checkout/pre-auth/${orderId}/${paymentMethod}`,
    };

    await db.Order.update({ tranId: transactionId, tranInitDate: moment(), paymentMethod }, { where: { id: orderId } });

    console.log("paymentMethod ", paymentMethod)

    if (paymentMethod == "cards") {

        const checkoutUrl = await initiatePaywayCheckout(paymentData)

        return { ...paymentData, checkoutUrl };
    } else {

        console.log(" ooo paymentData ooo ", paymentData)

        const respData = await initiatePaywayCheckout(paymentData)

        console.log(" ooo respData ooo ", respData.data)


        return { ...respData, id: orderTb.id }

    }
};

module.exports = checkoutPreauth;
