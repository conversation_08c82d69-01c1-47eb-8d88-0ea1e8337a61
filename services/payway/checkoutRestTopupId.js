const hostUrl = process.env.HOST_URL
const merchantId = process.env.ABA_MERCHANT_ID;
const apiKey = process.env.ABA_API_KEY;
const baseUrl = process.env.ABA_BASE_URL;
const alertPaymentToAdmin = require("@services/telegram/alertPaymentToAdmin")
const formatPhoneNumber = require("@apis/plasgate/formatPhoneNumber");

const moment = require("moment");

const db = require("@db");
const initiatePaywayCheckout = require("@apis/payway/initiatePaywayCheckout")
const generateHash = require("@services/payway/utils/generateHash");
const getPaywayConfig = require("@services/payway/utils/getPaywayConfig")

const checkoutRestTopupId = async (topupId, paymentMethod, receiptFile) => {

    const {
        returnUrl,
        returnDeeplink,
        successUrl,
        reqTime,
        abaScript,
        transactionId
    } = getPaywayConfig('/api/payment/aba/topup/callback/success')


    const topupTb = await db.Topup.findOne({
        where: { id: topupId },
    });


    if (topupTb == null) {
        return { errorMessage: "Top up is not found", stack: "Can't find top up with ID = " + topupId };
    }

    const userTb = await db.User.findOne({
        where: { id: topupTb.userId },
    });


    const paymentMethodTb = await db.PaymentMethod.findOne({
        where: {
            code: paymentMethod,
            status: 1
        },
    });

    if (paymentMethodTb == null) {
        return { errorMessage: "Payment method not found", stack: "Can't find payment method with CODE = " + paymentMethod, };
    }


    let hash = generateHash(
        apiKey,
        [
            reqTime,
            merchantId,
            transactionId,
            topupTb.amount,
            userTb.username,
            "purchase",
            paymentMethod,
            returnUrl,
            successUrl,
            returnDeeplink
        ]
    );

    await db.Topup.update({
        tranId: transactionId,
        tranInitDate: moment(),
        receiptFile,
        paymentMethod
    },
        { where: { id: topupId } });

    await topupTb.reload()


    const paymentData = {
        id: topupTb.id,
        baseUrl,
        reqTime,
        merchantId,
        transactionId,
        phone: userTb.username,
        amount: topupTb.amount,
        paymentOption: paymentMethod,
        type: "purchase",
        returnUrl,
        hash,
        successUrl,
        abaScript,
        checkoutUrl: `${hostUrl}/api/payment/aba/checkout/${topupTb.id}/${paymentMethod}`,
        returnDeeplink,
        receiptFile: topupTb.receiptFile
    };

    console.log(" paymentMethod " , paymentMethod)

    switch (paymentMethod) {
                    
        case "cards": 
         const checkoutUrl = await initiatePaywayCheckout(paymentData);
 
        return {...paymentData, checkoutUrl };

        case "ababank_transfer":

            topupTb.status = "IN-REVIEW";
            await topupTb.save();


            const message = `
                🚨 *SERVICE BOOKING ALERT* 🚨

👤 *Customer Information*
        *Name:* ${userTb.firstName} ${userTb.lastName}  
        *Phone:* ${formatPhoneNumber(userTb.username)}  

🛠️ *Top up information*
         *Transaction ID:* N/A
        *Payment Method:* ${paymentMethodTb.nameEn}
        *Paid Amount:* $${topupTb.amount.toFixed(2)}
        *Credit:* ${topupTb.credit.toFixed(2)} POINTS\n  
        *Transaction Date:* ${moment(topupTb.tranInitDate).format("DD-MM-YYYY hh:mm A")}  
        *Expiration Date:* ${topupTb.expDate == null ? "Never Expire" : moment(topupTb.expDate).format("DD-MM-YYYY hh:mm A")}\n                   
        *Current Balance:* $${userTb.balance.toFixed(2)}
        *Remark:* ${topupTb.remarkEn}`;

            alertPaymentToAdmin(message);

            return paymentData;

        case "beasy_point":

            if (userTb.balance < topupTb.totalAmount) {
                return { errorMessage: "Your bEasy point is insufficient!", stack: "Current balance is only " + userTb.balance, };
            } else {
                const balance = userTb.balance - topupTb.amount
                await db.User.update({ balance }, { where: { id: userTb.id } })
                topupTb.status = "PAID";
                await topupTb.save();
            }

            return paymentData;

        case "abapay_khqr_deeplink":
            const respData = await initiatePaywayCheckout(paymentData);

            return { ...respData, id: topupTb.id }


    }

};

module.exports = checkoutRestTopupId;
