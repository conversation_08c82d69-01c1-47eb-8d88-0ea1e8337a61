const hostUrl = process.env.HOST_URL;
const merchantId = process.env.ABA_MERCHANT_ID;
const apiKey = process.env.ABA_API_KEY;
const baseUrl = process.env.ABA_BASE_URL;

const moment = require("moment");
const db = require("@db");
const generateHash = require("@services/payway/utils/generateHash");
const getPaywayConfig = require("@services/payway/utils/getPaywayConfig")
const initiatePaywayCheckout = require("@apis/payway/initiatePaywayCheckout")

async function getCheckoutConfig(paymentMethodCode, amount, userId) {
  const {
    returnUrl,
    returnDeeplink,
    successUrl,
    reqTime,
    abaScript,
    transactionId
  } = getPaywayConfig('/api/payment/aba/topup/callback/success')

  const userTb = await db.User.findOne({ where: { id: userId } })
  const customerPhone = userTb.username;


  const paymentMethodTb = await db.PaymentMethod.findOne({
    where: {
      code: paymentMethodCode,
      status: 1,

    },
  });

  if (paymentMethodTb == null) {
    return { errorMessage: "Payment method not found", stack: "Can't find payment method with CODE = " + paymentMethodCode, };

  }

  let hash = generateHash(
    apiKey,
    [
      reqTime,
      merchantId,
      transactionId,
      amount,
      customerPhone,
      "purchase",
      paymentMethodCode,
      returnUrl,
      successUrl,
      returnDeeplink
    ]
  );

  const paymentData = {
    baseUrl,
    reqTime,
    merchantId,
    transactionId,
    phone: customerPhone,
    amount: amount,
    paymentOption: paymentMethodCode,
    type: "purchase",
    returnUrl,
    hash,
    successUrl,
    abaScript,
    returnDeeplink,
    balance: userTb.balance
  };
  return paymentData;
}


const checkoutRestTopupId = async (paymentMethodCode, topupId, userId, receiptFile) => {


  const bannerTb = await db.Banner.findOne({ where: { id: topupId, hasTopup: true } });

  if (bannerTb == null) {
    return { errorMessage: "Top-up id is not found!", stack: "Can't find top-up with CODE = " + paymentMethodCode };
  }

  const paymentData = await getCheckoutConfig(paymentMethodCode, bannerTb.amount, userId);

  const topupResp = await db.Topup.create({
    tranId: paymentData.transactionId,
    tranInitDate: moment(),
    paymentMethod: paymentMethodCode,
    amount: paymentData.amount,
    credit: bannerTb.credit,
    remarkEn: bannerTb.titleEn,
    remarkKm: bannerTb.titleKm,
    remarkVi: bannerTb.titleVi,
    remarkCn: bannerTb.titleCn,
    remarkTw: bannerTb.titleTw,
    balance: paymentData.balance,
    userId,
    receiptFile,
    status: paymentMethodCode == "ababank_trasfer" && receiptFile != null ? "IN-REVIEW" : "PENDING"
  });

  switch (paymentMethodCode) {
    case "cards":
      const checkoutUrl = await initiatePaywayCheckout(paymentData)
      return {
        ...paymentData,
        id: topupResp.id,
        checkoutUrl
      }
    case "ababank_transfer":
      return {
        ...paymentData,
        id: topupResp.id,
        receiptFile
      }

    default:
      const respData = await initiatePaywayCheckout(paymentData)
      return { ...respData, id: topupResp.id }

  }

}

const checkoutRestTopupAmount = async (paymentMethodCode, amount, userId, receiptFile) => {


  const paymentData = await getCheckoutConfig(paymentMethodCode, amount, userId);

  const topupResp = await db.Topup.create({
    tranId: paymentData.transactionId,
    tranInitDate: moment(),
    paymentMethod: paymentMethodCode,
    amount: paymentData.amount,
    credit: paymentData.amount,
    remarkEn: "You've successfully topped up!",
    remarkZh: "您已成功充值!",
    remarkKh: "អ្នកបានបញ្ចូលប្រាក់បានជោគជ័យ!",
    balance: paymentData.balance,
    userId,
    receiptFile,
    status: paymentMethodCode == "ababank_trasfer" && receiptFile != null ? "IN-REVIEW" : "PENDING"
  });

  switch (paymentMethodCode) {

    case "cards":
      const checkoutUrl = await initiatePaywayCheckout(paymentData)
      return {
        ...paymentData,
        id: topupResp.id,
        checkoutUrl
      }

    case "ababank_transfer":
      await db.Topup.update({ status: receiptFile != null ? "IN-REVIEW" : "PENDING" }, { where: { id: topupResp.id } })

      return { ...paymentData, id: topupResp.id, receiptFile: topupResp.receiptFile }

    case "abapay_khqr_deeplink":
      const respData = await initiatePaywayCheckout(paymentData)
      return { ...respData, id: topupResp.id }

    default: return null
  }


};

module.exports = { checkoutRestTopupId, checkoutRestTopupAmount };
