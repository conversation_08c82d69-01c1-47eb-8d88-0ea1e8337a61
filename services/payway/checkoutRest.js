const hostUrl = process.env.HOST_URL
const merchantId = process.env.ABA_MERCHANT_ID;
const apiKey = process.env.ABA_API_KEY;
const baseUrl = process.env.ABA_BASE_URL;


const moment = require("moment");

const db = require("@db");
const initiatePaywayCheckout = require("@apis/payway/initiatePaywayCheckout")
const generateHash = require("@services/payway/utils/generateHash");
const getPaywayConfig = require("@services/payway/utils/getPaywayConfig")

const checkoutRest = async (orderId, paymentMethod, receiptFile) => {

    let {
        returnUrl,
        returnDeeplink,
        successUrl,
        reqTime,
        abaScript,
        transactionId
    } = getPaywayConfig('/api/payment/aba/callback/success')


    const orderTb = await db.Order.findOne({
        where: { id: orderId },

    });


    const orderTbs = await db.Order.findAll({
        where: { bulkOrderId: orderTb.bulkOrderId },
        include: [
            { as: 'orderDetails', model: db.OrderDetail },
        ]
    });

    if (orderTbs.length == 0) {
        return { errorMessage: "Order ID not found", stack: "Can't find order with ID = " + orderId };
    }


    const paymentMethodTb = await db.PaymentMethod.findOne({
        where: {
            code: paymentMethod,
            status: 1
        },
    });


    if (paymentMethodTb == null) {
        return { errorMessage: "Payment method not found", stack: "Can't find payment method with CODE = " + paymentMethod, };
    }


    let totalAmount = 0;
    let vat = 0.0;
    let customerPhone = ""
    let bulkOrderId = ""

    for (let orderTb of orderTbs) {
        console.log(" orderTb.amount  ", orderTb.amount)

        totalAmount = totalAmount + (orderTb.amount - orderTb.discount + orderTb.transportFee + orderTb.serviceFee)
        vat = orderTb.vat
        customerPhone = orderTb.customerPhone
        bulkOrderId = orderTb.bulkOrderId
    }

    totalAmount = totalAmount + (totalAmount * vat)


    // returnDeeplink = paymentMethod == "cards" ? "" : returnDeeplink
    // const type = "purchase"
    const type = "pre-auth"
    let hash = generateHash(
        apiKey,
        [
            reqTime,
            merchantId,
            transactionId,
            totalAmount,
            customerPhone,
            type,
            paymentMethod,
            returnUrl,
            successUrl,
            returnDeeplink
        ]
    );


    await db.Order.update({
        tranId: transactionId,
        tranInitDate: moment(),
        paymentMethod,
        paymentMethodDisplay: paymentMethodTb.nameEn,
        receiptFile
    },
        { where: { bulkOrderId: bulkOrderId } });



    const paymentData = {
        id: bulkOrderId,
        baseUrl,
        reqTime,
        merchantId,
        transactionId,
        phone: customerPhone,
        amount: totalAmount,
        paymentOption: paymentMethod,
        type,
        returnUrl,
        hash,
        successUrl,
        abaScript,
        checkoutUrl: `${hostUrl}/api/payment/aba/checkout/${orderId}/${paymentMethod}`,
        returnDeeplink,
        receiptFile: ""
    };

    console.log(" generate hash ", [
        reqTime,
        merchantId,
        transactionId,
        totalAmount,
        customerPhone,
        type,
        paymentMethod,
        returnUrl,
        successUrl,
        returnDeeplink
    ])
    console.log(" paymentData ", paymentData)

    switch (paymentMethod) {
        case "cards":
            const checkoutUrl = await initiatePaywayCheckout(paymentData)
            return { ...paymentData, checkoutUrl };

        case "ababank_transfer":

            await db.Order.update({
                paymentStatus: "IN-REVIEW",
            }, { where: { bulkOrderId: bulkOrderId } });

            return paymentData;
        case "beasy_point":
            const userTb = await db.User.findOne({ where: { id: orderTbs[0].userId } })
            if (userTb.balance < totalAmount) {
                return { errorMessage: "Your bEasy point is insufficient!", stack: "Current balance is only " + userTb.balance, };
            } else {
                const balance = userTb.balance - totalAmount
                await db.User.update({ balance }, { where: { id: userTb.id } })
                await db.Order.update({
                    paymentStatus: "PAID",
                }, { where: { bulkOrderId: bulkOrderId } });
            }

            return paymentData;

        case "abapay_khqr_deeplink":
            const respData = await initiatePaywayCheckout(paymentData);

            return { ...respData, id: bulkOrderId }


    }

};

module.exports = checkoutRest;
