const moment = require("moment");
const db = require("@db");

const hostUrl = process.env.HOST_URL
const generateHash = require("@services/payway/utils/generateHash")
const getPaywayConfig = require("@services/payway/utils/getPaywayConfig")
const initiatePaywayCheckout = require("@apis/payway/initiatePaywayCheckout")

const merchantId = process.env.ABA_MERCHANT_ID;
const apiKey = process.env.ABA_API_KEY;

const baseUrl = process.env.ABA_BASE_URL;

const checkoutBulkPreauth = async (bulkOrderId, paymentMethod) => {

    const {
        cancelUrl,
        returnDeeplink,
        returnUrl,
        successUrl,
        reqTime,
        abaScript,
        transactionId
    } = getPaywayConfig('/api/payment/aba/callback/success')

    const type = "pre-auth"

    const orderTbs = await db.Order.findAll({
        where: { bulkOrderId },
        include: [
            {
                model: db.OrderDetail,
                as: 'orderDetails'
            }
        ]
    });



    if (orderTbs.length == 0) {
        return { errorMessage: "Order ID not found", stack: "Can't find order with ID = " + orderId };
    }

    const paymentMethodTb = await db.PaymentMethod.findOne({
        where: {
            code: paymentMethod,
            status: 1
        },
    });

    if (paymentMethodTb == null) {
        return { errorMessage: "Payment method not found", stack: "Can't find payment method with CODE = " + paymentMethod, };
    }


    let totalAmount = 0.0;
    let vat = 0.1;

    for (let orderTb of orderTbs) {
        let order = orderTb.dataValues;
        let orderDetails = orderTb.orderDetails;
        let addOnAmt = 0;

        for (let o of orderDetails) {
            addOnAmt = addOnAmt + (o.amount * o.qty)
        }

        vat = order.vat
        totalAmount = totalAmount + (order.amount + order.serviceFee + order.transportFee - order.discount)

    }

    totalAmount = totalAmount + (vat * totalAmount)

    console.log(" totalAmount ", totalAmount)

    let hash = generateHash(
        apiKey,
        [
            reqTime,
            merchantId,
            transactionId,
            totalAmount,
            orderTbs[0].customerPhone,
            type,
            paymentMethod,
            returnUrl,
            successUrl,
            returnDeeplink
        ]
    );


    const paymentData = {
        bulkOrderId,
        id: orderTbs[0].id,
        baseUrl,
        reqTime,
        merchantId,
        transactionId,
        phone: orderTbs[0].customerPhone,
        amount: totalAmount,
        paymentOption: paymentMethod,
        type,
        returnUrl,
        hash,
        successUrl,
        abaScript,
        returnDeeplink,
        checkoutUrl: `${hostUrl}/api/payment/aba/checkout/pre-auth/${bulkOrderId}/${paymentMethod}`,
    };


    await db.Order.update({ tranId: transactionId, tranInitDate: moment(), paymentMethod }, { where: { bulkOrderId } });

    if (paymentMethod == "cards") {

        const checkoutUrl = await initiatePaywayCheckout(paymentData)

        return { ...paymentData, checkoutUrl };
    } else {

        const respData = await initiatePaywayCheckout(paymentData)
        return { ...respData, id: orderTbs[0].id, bulkOrderId }

    }
};

module.exports = checkoutBulkPreauth;
