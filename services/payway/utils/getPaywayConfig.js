const hostUrl = process.env.HOST_URL
const moment = require("moment");
const getTransactionId = require("@services/payway/utils/getTransactionId");

let returnUrl = Buffer.from(
    `${hostUrl}/api/payment/aba/topup/callback/success`
).toString("base64");

const getPaywayConfig = (callbackUrl) => {

    if (callbackUrl) {
        returnUrl = Buffer.from(
            `${hostUrl}${callbackUrl}`
        ).toString("base64");
    }

    let cancelUrl = `https://payment.beasy.info?hide-close=2`;
    let successUrl = `https://payment.beasy.info?hide-close=2`;

    const iosDeeplink = 'https://api.beasy.info/success';
    // const androidDeeplink = 'https://beasy.onelink.me/n7UZ/ababank';
    const androidDeeplink = 'https://api.beasy.info';

    const returnDeeplink = Buffer.from(JSON.stringify({
        ios_scheme: iosDeeplink,
        android_scheme: androidDeeplink,
    })).toString('base64');

    let abaScript = "https://checkout.payway.com.kh/plugins/checkout2-0.js?hide-close=2";

    if (process.env.NODE_ENV != "production") {
        abaScript = "https://checkout.payway.com.kh/plugins/checkout2-0-dev.js?hide-close=2";
    }

    let reqTime = moment().format("YYYYMMDDHHmmss");
    const transactionId = getTransactionId();

    return {
        cancelUrl,
        returnUrl,
        successUrl,
        returnDeeplink,
        reqTime,
        abaScript,
        transactionId
    }
}

module.exports = getPaywayConfig