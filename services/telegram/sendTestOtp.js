const TELEGRAM_API_URL = process.env.TELEGRAM_API_URL
const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN
const TELEGRAM_ORDER_ALERT_CHAT_ID = process.env.TELEGRAM_ORDER_ALERT_CHAT_ID

const axios = require('axios')

const sendTestOtp = async(phone,otp) => {

    try {
        const telegramUrl = `${TELEGRAM_API_URL}/bot${TELEGRAM_BOT_TOKEN}/sendMessage`;
        console.log("telegramUrl", telegramUrl)
        const response = await axios.post(telegramUrl, {
            chat_id: TELEGRAM_ORDER_ALERT_CHAT_ID,
            text: `bEasy verification code is ${otp}, Sent to +${phone}.`,
            parse_mode: 'Markdown' // or 'HTML'
        });
        return { success: true, telegram_response: response.data };
    } catch (error) {
        console.error('❌ Telegram API error:', error.response?.data || error.message);
        return {error: 'Failed to send message to Telegram'};
    }

}

module.exports = sendTestOtp