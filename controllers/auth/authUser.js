// const jwt = require('jsonwebtoken');
// const { User } = require('@db');

// const authUser = async (req, res, next) => {
//   try {
//     const authHeader = req.headers['authorization'];
//     const token = authHeader && authHeader.split(' ')[1];

//     if (!token) {
//       return res.status(401).json({
//         success: false,
//         message: 'Access token required'
//       });
//     }

//     const decoded = jwt.verify(token, process.env.JWT_SECRET);

//     const user = await User.findByPk(decoded.id, {});

//     if (!user) {
//       return res.status(401).json({
//         success: false,
//         message: 'Invalid token - user not found'
//       });
//     }

//     // Block GUSTMODE users from authentication
//     if (user.type === "GUSTMODE") {
//       return res.status(403).json({
//         success: false,
//         message: 'Guest mode users cannot access authenticated resources'
//       });
//     }

//     if (user.status !== "ACTIVE") {
//       return res.status(403).json({
//         success: false,
//         message: 'User account is not active'
//       });
//     }

//     req.user = {
//       userId: user.id,
//       username: user.username,
//       type: user.type,
//       status: user.status,
//     };

//     next();
//   } catch (error) {
//     if (error.name === 'JsonWebTokenError') {
//       return res.status(401).json({
//         success: false,
//         message: 'Invalid token'
//       });
//     }

//     if (error.name === 'TokenExpiredError') {
//       return res.status(401).json({
//         success: false,
//         message: 'Token expired'
//       });
//     }

//     return res.status(500).json({
//       success: false,
//       message: 'Token verification failed',
//       error: error.message
//     });
//   }
// };

// const requireRole = (requiredRoles) => {
//   return (req, res, next) => {
//     if (!req.user) {
//       return res.status(401).json({
//         success: false,
//         message: 'Authentication required'
//       });
//     }

//     const userRole = req.user.type;
//     const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

//     if (!roles.includes(userRole)) {
//       return res.status(403).json({
//         success: false,
//         message: 'Insufficient permissions'
//       });
//     }

//     next();
//   };
// };

// // const requireAdmin = requireRole(['ADMIN']);
// // const requireManager = requireRole(['MANAGER', 'ADMIN']);
// // const requireHR = requireRole(['HR', 'ADMIN']);

// const optionalAuth = async (req, res, next) => {
//   try {
//     const authHeader = req.headers['authorization'];
//     const token = authHeader && authHeader.split(' ')[1];

//     if (token) {
//       const decoded = jwt.verify(token, process.env.JWT_SECRET);
//       const user = await User.findByPk(decoded.id);

//       if (user && user.type !== 'GUSTMODE') {
//         req.user = {
//           userId: user.id,
//           username: user.username,
//           type: user.type,
//           status: user.status,
//         };
//       }
//     }

//     next();
//   } catch (error) {
//     next();
//   }
// };

// module.exports = {
//   authUser,
//   requireRole,
//   // requireAdmin,
//   // requireManager,
//   // requireHR,
//   optionalAuth
// };

const jwt = require('jsonwebtoken');
const { User } = require('@db');

const authUser = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    // If no token, create guest user
    if (!token) {
      const guestUser = await User.create({
        type: 'GUSTMODE',
        status: 'ACTIVE',
        username: `guest_${Date.now()}`,
        balance: 0.00
      });

      const guestToken = jwt.sign(
        { id: guestUser.id },
        process.env.JWT_SECRET,
        { expiresIn: '30d' }
      );

      req.user = {
        userId: guestUser.id,
        username: guestUser.username,
        type: guestUser.type,
        status: guestUser.status,
        isGuest: true,
        token: guestToken
      };

      res.set('X-Guest-Token', guestToken);
      return next();
    }

    // Verify token for authenticated users
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.id);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token - user not found'
      });
    }

    req.user = {
      userId: user.id,
      username: user.username,
      type: user.type,
      status: user.status,
      isGuest: user.type === 'GUSTMODE',
      token: token
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Authentication failed',
      error: error.message
    });
  }
};

const checkPaymentPermission = (req, res, next) => {
  if (req.user?.isGuest) {
    return res.status(403).json({
      success: false,
      message: 'Guest users cannot perform payment actions'
    });
  }
  next();
};

module.exports = {
  authUser,
  checkPaymentPermission
};