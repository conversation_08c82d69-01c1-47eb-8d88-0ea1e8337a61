const jwt = require('jsonwebtoken');
const { User } = require('@db');

// Verify JWT token
const authAdmin = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
 
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    const user = await User.findByPk(decoded.id, {});

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token - user not found'
      });
    }

    if (user.type != "ADMIN") {
      return res.status(401).json({
        success: false,
        message: 'Invalid token - access to unahtorized resources'
      });
    }

    req.user = {
      userId: user.id,
      username: user.username,
      type: user.type,
      status: user.status,
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Token verification failed',
      error: error.message
    });
  }
};

// Check if user has required role
const requireRole = (requiredRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userRole = req.user.roleName;
    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

    if (!roles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

// Check if user is admin
const requireAdmin = requireRole(['Admin', 'Super Admin']);

// Check if user is manager or admin
const requireManager = requireRole(['Manager', 'Admin', 'Super Admin']);

// Check if user is HR or admin
const requireHR = requireRole(['HR', 'Admin', 'Super Admin']);

// Optional authentication (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await Users.findByPk(decoded.userId, {
        include: [
          {
            association: 'roles',
            attributes: ['id', 'name']
          }
        ]
      });

      if (user) {
        req.user = {
          userId: user.id,
          username: user.username,
          roleId: user.roleId,
          roleName: user.role?.name
        };
      }
    }

    next();
  } catch (error) {
    // Continue without authentication if token is invalid
    next();
  }
};

module.exports = {
  authAdmin,
  requireRole,
  requireAdmin,
  requireManager,
  requireHR,
  optionalAuth
};

