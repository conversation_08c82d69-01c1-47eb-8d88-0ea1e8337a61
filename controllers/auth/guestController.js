const { User } = require("@db");
const jwt = require("jsonwebtoken");

class GuestController {
  static async createGuest(req, res) {
    try {
      const userExists = await User.findOne({
        where: {
          type: "GUSTMODE",
          status: "ACTIVE",
        },
      });

      if (userExists) {
        guestUser = userExists[0];
      } else {
        guestUser = await User.create({
          type: "GUSTMODE",
          status: "ACTIVE",
          username: `guest_1`,
          balance: 0.0,
          language: "en",
          status: "ACTIVE",
        });
      }

      const token = jwt.sign({ id: guestUser.id }, process.env.JWT_SECRET, {
        expiresIn: "24h",
      });

      return res.status(201).json({
        success: true,
        data: {
          user: {
            id: guestUser.id,
            username: guestUser.username,
            type: guestUser.type,
            isGuest: true,
          },
          token,
        },
      });
    } catch (error) {
      console.log(error.message);
      return res.status(500).json({
        success: false,
        message: "Failed to create guest session",
        error: error.message,
      });
    }
  }

  static async convertToRegular(req, res) {
    try {
      const { id } = req.user;
      const { username, password, email } = req.body;

      const user = await User.findByPk(id);
      if (!user || user.type !== "GUSTMODE") {
        return res.status(400).json({
          success: false,
          message: "Invalid guest user",
        });
      }

      const updatedUser = await user.update({
        username,
        password,
        email,
        type: "USER",
        status: "ACTIVE",
      });

      return res.json({
        success: true,
        message: "Account upgraded successfully",
        data: {
          id: updatedUser.id,
          username: updatedUser.username,
          type: updatedUser.type,
        },
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Conversion failed",
        error: error.message,
      });
    }
  }
}

module.exports = GuestController;
