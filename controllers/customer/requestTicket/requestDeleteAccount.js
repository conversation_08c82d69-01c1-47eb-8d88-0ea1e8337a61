
const jwt = require('jsonwebtoken');
const db = require("@db")

const requestDeleteAccount = async (req, res, next) => {
  const username = `${req.params.username}`;

  const authHeader = req.headers['authorization'];
  const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

  if (!token) {
    return res.error('Token not provided', res.HttpStatus.UNAUTHORIZED)
  }


  const decodedUser = jwt.decode(token);
  const userTb = await db.User.findOne({ where: { id: decodedUser.id, status: "ACTIVE" }, attributes: { exclude: ["createdAt"] } })
    if(userTb == null){
        return res.error('Account not found!',res.HttpStatus.NOT_FOUND)
    }
  userTb.status = 'DELETED';
  userTb.username =  `${ userTb.username}-${userTb.id}-DELETED`
  await userTb.save();

  return res.success(userTb)

};


module.exports = require("@asyncHandler")(requestDeleteAccount)