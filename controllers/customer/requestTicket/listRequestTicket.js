const jwt = require('jsonwebtoken');
const db = require("@db")

const listRequestTicket = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1];
    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const { status, type } = req.query

    const decodedUser = jwt.decode(token);

    let sqlStr = { userId: decodedUser.id }

    if (status != undefined) {
        sqlStr = { ...sqlStr, status }
    }

    if (type != undefined) {
        sqlStr = { ...sqlStr, type }
    }

 
    const userRequestTicket = await db.UserRequestTicket.findAll({ where: sqlStr })

    return res.success(userRequestTicket)
}

module.exports = require("@asyncHandler")(listRequestTicket)