const jwt = require('jsonwebtoken');
const db = require("@db")

const createRequestTicket = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>
    const { subject, content } = req.body

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);

    const userRequestTicket = await db.UserRequestTicket.create({ subject, content, userId: decodedUser.id })

    return res.success(userRequestTicket)
}

module.exports = require("@asyncHandler")(createRequestTicket)
 