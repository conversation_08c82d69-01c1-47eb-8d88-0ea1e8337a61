// const jwt = require('jsonwebtoken');
// const db = require("@db")
// const listCategoryService = require("@services/category/listCategory")
// const listBannerService = require("@services/banner/listBanner")

// const getHomepage = async (req, res, next) => {

//     const authHeader = req.headers['authorization'];
//     const token = authHeader?.split(' ')[1]; // Format: Bearer <token>
//     let balance = "0.00";
//     let addressTb = null;

//     if (token) {
//         const decodedUser = jwt.decode(token);

//         userTb = await db.User.findOne({ where: { id: decodedUser.id } })
//         addressTb = await db.UserAddress.findAll({
//             attributes: ["id", "isPrimary", "name", "address", "addressDetail", "note", "latitude", "longitude", "sort", "bookingDate"],
//             where: { userId: decodedUser.id, isPrimary: true },
//             order: [
//                 ['sort', 'DESC'],
//                 ['updatedAt', 'DESC']
//             ]
//         });
//         balance = userTb.balance.toFixed(2);

//     }

//     const categoryList = await listCategoryService({ status: 1 }, [
//         "id", "nameEn", "nameKm", "nameVi", "nameCn", "nameTw",
//         "iconUrl", "isComingSoon",
//         "noteEn", "noteKm", "noteVi", "noteCn", "noteTw",
//         "taskInfoEn", "taskInfoKm", "taskInfoVi", "taskInfoCn", "taskInfoTw",
//     ])

//     const bannerList = await listBannerService({ type: 'HOME-BANNER', status: 1 },
//         [
//             "id", "name", "hasDetail", "type", "hasTopup",
//             "imgUrlEn", "imgUrlKm", "imgUrlVi", "imgUrlCn", "imgUrlTw",
//             "titleEn", "titleKm", "titleVi", "titleCn", "titleTw",
//             "contentEn", "contentKm", "contentVi", "contentCn", "contentTw",

//             "deeplink", "deeplinkType"
//         ])

//     const productBundleList = await listBannerService({ type: 'PRODUCT-BUNDLE', status: 1 },
//         [
//             "id", "name", "hasDetail", "type", "hasTopup",
//             "imgUrlEn", "imgUrlKm", "imgUrlVi", "imgUrlCn", "imgUrlTw",
//             "titleEn", "titleKm", "titleVi", "titleCn", "titleTw",
//             "contentEn", "contentKm", "contentVi", "contentCn", "contentTw",
//             "deeplink", "deeplinkType"
//         ])

//     return res.success({
//         categoryList,
//         bannerList,
//         productBundleList,
//         balance: parseFloat(balance).toLocaleString('en-US', {
//             minimumFractionDigits: 2,
//             maximumFractionDigits: 2
//         }),
//         primaryAddress: addressTb
//     })

// }

// module.exports = require("@asyncHandler")(getHomepage)

const jwt = require("jsonwebtoken");
const db = require("@db");
const listCategoryService = require("@services/category/listCategory");
const listBannerService = require("@services/banner/listBanner");

const getHomepage = async (req, res) => {
  try {
    const authHeader = req.headers["authorization"];
    const token = authHeader?.split(" ")[1];
    let balance = "0.00";
    let addressTb = null;
    let isGuest = true;

    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await db.User.findByPk(decoded.id);

        if (user && user.type !== "GUSTMODE") {
          isGuest = false;
          balance = user.balance.toFixed(2);

          addressTb = await db.UserAddress.findAll({
            where: { userId: decoded.id, isPrimary: true },
            order: [
              ["sort", "DESC"],
              ["updatedAt", "DESC"],
            ],
            attributes: [
              "id",
              "isPrimary",
              "name",
              "address",
              "addressDetail",
              "note",
              "latitude",
              "longitude",
              "sort",
              "bookingDate",
            ],
          });
        }
      } catch (error) {
        console.error("Token verification failed:", error.message);
      }
    }

    const [categoryList, bannerList, productBundleList] = await Promise.all([
      listCategoryService({ status: 1 }, [
        "id",
        "nameEn",
        "nameKm",
        "nameVi",
        "nameCn",
        "nameTw",
        "iconUrl",
        "isComingSoon",
        "noteEn",
        "noteKm",
        "noteVi",
        "noteCn",
        "noteTw",
        "taskInfoEn",
        "taskInfoKm",
        "taskInfoVi",
        "taskInfoCn",
        "taskInfoTw",
      ]),
      listBannerService({ type: "HOME-BANNER", status: 1 }, [
        "id",
        "name",
        "hasDetail",
        "type",
        "hasTopup",
        "imgUrlEn",
        "imgUrlKm",
        "imgUrlVi",
        "imgUrlCn",
        "imgUrlTw",
        "titleEn",
        "titleKm",
        "titleVi",
        "titleCn",
        "titleTw",
        "contentEn",
        "contentKm",
        "contentVi",
        "contentCn",
        "contentTw",
        "deeplink",
        "deeplinkType",
      ]),
      listBannerService({ type: "PRODUCT-BUNDLE", status: 1 }, [
        "id",
        "name",
        "hasDetail",
        "type",
        "hasTopup",
        "imgUrlEn",
        "imgUrlKm",
        "imgUrlVi",
        "imgUrlCn",
        "imgUrlTw",
        "titleEn",
        "titleKm",
        "titleVi",
        "titleCn",
        "titleTw",
        "contentEn",
        "contentKm",
        "contentVi",
        "contentCn",
        "contentTw",
        "deeplink",
        "deeplinkType",
      ]),
    ]);

    return res.success({
      categoryList,
      bannerList,
      productBundleList,
      balance: parseFloat(balance).toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }),
      primaryAddress: addressTb,
      isGuest,
    });
  } catch (error) {
    console.error("Homepage error:", error);
    return res.error("Failed to load homepage");
  }
};

module.exports = require("@asyncHandler")(getHomepage);
