
const db = require("@db")

const getCategoryProduct = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>
    const categoryId = req.params.id
    if (!token) {
        return res.error('Token not provided', res.HttpStatus.UNAUTHORIZED)
    }

    const resp = await db.Product.findAll({
        where: { categoryId, status: 1 },
        attributes: {
            exclude: ["createdAt", "updatedAt", "categoryId", "sort", "amount", "status", "hasExtra"]
        },

    })

    return res.success(resp)

}

module.exports = require("@asyncHandler")(getCategoryProduct)