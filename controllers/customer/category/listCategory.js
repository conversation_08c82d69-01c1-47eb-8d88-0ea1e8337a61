const listCategoryService = require("@services/category/listCategory")

const listCategory = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.error('Token not plistCategoryrovided', res.HttpStatus.UNAUTHORIZED)
    }

    const categoryId = req.query.id

    const categories = await listCategoryService({ ...req.query, status: 1 }, [
        "id",
        "nameEn", "nameKm", "nameVi", "nameCn", "nameTw",
        "iconUrl", "isComingSoon",
        "noteEn", "noteKm", "noteVi", "noteCn", "noteTw",
        "taskInfoEn", "taskInfoKm", "taskInfoCn", "taskInfoTw", "taskInfoVi"
    ])

    const resp = categories.filter(category => {
        if (category.dataValues.id != categoryId) {
            return category
        }
    })

    return res.success(resp)

}

module.exports = require("@asyncHandler")(listCategory)