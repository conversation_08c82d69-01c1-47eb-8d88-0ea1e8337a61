const db = require("@db")
const jwt = require('jsonwebtoken');

const createAddress = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }


    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })
    await db.UserAddress.update({ isPrimary: false }, { where: { userId: userTb.id } })
    const resp = await userTb.createUserAddress({ ...req.body })

    return res.success(resp)


}


module.exports = require("@asyncHandler")(createAddress)