const db = require("@db")
const jwt = require('jsonwebtoken');

const updateAddress = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.error('Token not provided');
    }


    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })
    const updateCount = await db.UserAddress.update(req.body, { where: { id: req.body.id, userId: userTb.id } })

    if (updateCount == 0) {
        return res.error("Update is unsuccessful")
    }

    const updatedAddress = await db.UserAddress.findOne({ where: { id: req.body.id, userId: userTb.id } })

    return res.success(updatedAddress)


}


module.exports = require("@asyncHandler")(updateAddress)