const db = require("@db")
const jwt = require('jsonwebtoken');

const listAddress = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }


    const decodedUser = jwt.decode(token);
    const resp = await db.UserAddress.findAll({
        attributes: ["id", "isPrimary", "name", "address", "addressDetail", "floorNum" , "roomNum", "note", "latitude", "longitude","sort", "bookingDate"],
        where: { userId: decodedUser.id },
        order: [
            ['sort', 'DESC'],
            ['updatedAt', 'DESC']
        ]
    });

    return res.success(resp)


}


module.exports = require("@asyncHandler")(listAddress)