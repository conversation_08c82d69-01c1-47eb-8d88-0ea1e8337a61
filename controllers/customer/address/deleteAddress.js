const db = require("@db")
const jwt = require('jsonwebtoken');
const { error } = require("winston");

const deleteAddress = async (req, res, next) => {

    const addressId = req.params.id
    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);

    const deletedCount = await db.UserAddress.destroy({ where: { userId: decodedUser.id, id: addressId } })

    if(deletedCount > 0){
        return res.success(null, `${deletedCount} records are deleted`)
    }

    return res.error('Delete address is unsuccessful')


}


module.exports = require("@asyncHandler")(deleteAddress)