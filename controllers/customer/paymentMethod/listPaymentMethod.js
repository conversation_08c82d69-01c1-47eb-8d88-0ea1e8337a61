const db = require("@db");
const { Op } = require("sequelize");

const listPaymentMethod = async (req, res, next) => {

    let paymentMethodTb = []

    switch (req.query.type) {
        case 'BOOKING':
        case 'BOOKING-HISTORY':
            paymentMethodTb = await db.PaymentMethod.findAll({
                attributes: { exclude: ["status", "createdAt", "updatedAt"] },
                where: { status: true, code: { [Op.in]: ["beasy_point","abapay_khqr_deeplink", "cards"] } },
                order: [['sort', 'asc']]
            });
            break;
        case 'TOPUP':
            paymentMethodTb = await db.PaymentMethod.findAll({
                attributes: { exclude: ["status", "createdAt", "updatedAt"] },
                where: { status: true, code: { [Op.notIn]: ["beasy_point"] } },
                order: [['sort', 'asc']]
            });
            break;
        case 'BUNDLE-TOPUP':
            paymentMethodTb = await db.PaymentMethod.findAll({
                attributes: { exclude: ["status", "createdAt", "updatedAt"] },
                where: { status: true, code: { [Op.notIn]: ["beasy_point"] } },
                order: [['sort', 'asc']]
            });
            break;

        default:
            paymentMethodTb = await db.PaymentMethod.findAll({
                attributes: { exclude: ["status", "createdAt", "updatedAt"] },
                where: { status: true },
                order: [['sort', 'asc']]
            });
    }


    return res.success(paymentMethodTb)
}

module.exports = require("@asyncHandler")(listPaymentMethod)