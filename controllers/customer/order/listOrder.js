const db = require("@db")
const { Op } = require("sequelize")
const jwt = require('jsonwebtoken');
const alertOrderToAdmin = require("@services/telegram/alertOrderToAdmin")
const moment = require('moment');

const listOrder = async (req, res, next) => {
    
    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);

    let strSql = { userId: decodedUser.id }

    if (req.query.hasOwnProperty("status") && req.query.status != "null") {
        strSql = { ...strSql, status: req.query.status }
    }

    const orderTbs = await db.Order.findAll({
        where: strSql,
        order: [["id", "DESC"]],
        include: [
            {
                model: db.ProductOption, as: 'productOption',
                attributes: ["id", "amount", "nameEn", "nameKm", "nameCn", "nameTw", "amount"],
                include: [
                    {
                        model: db.Product, as: "product",
                        attributes: ["nameEn", "nameKm", "nameCn", "nameTw", "nameVi"],
                        include: [
                            {
                                model: db.Category, as: "category",
                                attributes: ["nameEn", "nameKm", "nameVi", "nameCn", "nameTw"],
                            }
                        ]
                    }
                ],
            },
            {
                model: db.OrderDetail, as: 'orderDetails',
                attributes: ["id", "amount", "nameEn", "nameKm","nameVi", "nameTw", "nameCn", "amount"],
            }
        ]
    })

    return res.success(orderTbs)


}

module.exports = require("@asyncHandler")(listOrder)