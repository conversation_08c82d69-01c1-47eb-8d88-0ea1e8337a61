const db = require("@db")

const getCancellationStatus = async (req, res, next) => {

    const orderId = req.params.id

    const orderTb = await db.Order.findOne({ where: { id: orderId } })


    if(!orderTb){
   return res.error("Order not found!")
    }

    if (['PENDING', 'ACCEPTED'].includes(orderTb.status)) {
        return res.success("Order can be cancelled")
    } else {
        return res.error("Order can not be cancelled")
    }
}

module.exports = require("@asyncHandler")(getCancellationStatus)