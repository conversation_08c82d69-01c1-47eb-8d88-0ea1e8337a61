
const jwt = require('jsonwebtoken');

const serviceFee = parseFloat(process.env.SERVICE_FEE)
const transportFee = parseFloat(process.env.TRANSPORT_FEE)

const getBulkOrderPreviewService = require("@services/order/getOrderPreview")
const getOrderCustomerInfoService = require("@services/order/getOrderCustomerInfo")

const getBulkOrderPreview = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }
    const { products, addressId, paymentMethod, scheduleStartDate, note } = req.body
    const decodedUser = jwt.decode(token);

    const custInfo = await getOrderCustomerInfoService(decodedUser.id, addressId);

    let orderPreviewList = []
    let discount = 0
    let amount = 0


    for (const product of products) {

        const orderPreview = await getBulkOrderPreviewService(
            product.productOptionId,
            product.productAddOnIds || [],
            scheduleStartDate,
            custInfo.language
        );
        orderPreviewList.push(orderPreview)
        amount = amount + orderPreview.amount
    }

    const totalAmount = amount + transportFee + serviceFee
    const vatFee = totalAmount * 0.1
    const totalPayableAmount = totalAmount + vatFee

    const resp = {
        ...custInfo,
        amount: `$${amount.toFixed(2)}`,
        discount: `($${discount.toFixed(2)})`,
        totalAmount: `($${totalAmount.toFixed(2)})`,
        serviceFee: `$${serviceFee.toFixed(2)}`,
        transportFee: `$${transportFee.toFixed(2)}`,
        vatFee: `$${vatFee.toFixed(2)}`,
        totalPayableAmount: `$${totalPayableAmount.toFixed(2)}`,
        paymentMethod,
        scheduleStartDate,
        note,
        orderPreviewList
    }

    return res.success(resp)
}

module.exports = require("@asyncHandler")(getBulkOrderPreview)