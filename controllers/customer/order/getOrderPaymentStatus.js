const db = require("@db")

const getOrderPaymentStatus = async (req, res, next) => {


    const paymentStatus = await db.Order.findOne({ where: { id: req.params.id }, attributes: ["id", "status", "paymentStatus"], })

    if (paymentStatus) {
        return res.success(paymentStatus)
    } else {
        return res.error("Order not found!")
    }


}

module.exports = require("@asyncHandler")(getOrderPaymentStatus)