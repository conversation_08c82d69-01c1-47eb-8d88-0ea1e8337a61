const db = require("@db")
const { Op } = require("sequelize")
const moment = require('moment');
const jwt = require('jsonwebtoken');
const formatPhoneNumber = require("@apis/plasgate/formatPhoneNumber")
const cancelPreAuthApi = require("@apis/payway/cancelPreAuthApi")
const alertOrderToAdmin = require("@services/telegram/alertOrderToAdmin")

const cancelOrderV2 = async (req, res, next) => {

    const bulkOrderId = req.params.id

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);

    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })

    const orderDetailTb = await db.Order.findOne({
        attributes: { exclude: ["userId", "productOptionId", "updatedAt"] },
        where: {
            userId: decodedUser.id,
            [Op.or]: [
                { bulkOrderId: bulkOrderId }
            ]
        },
        include: [
            {
                model: db.ProductOptionV2, as: 'productOptionV2',
                attributes: { exclude: ["createdAt", "updatedAt", "sort", "productId"] },
                include: [
                    {
                        model: db.Product, as: 'product',
                        attributes: { exclude: ["createdAt", "updatedAt", "categoryId", "sort", "status", "amount"] },
                        include: [
                            {
                                model: db.Category, as: 'category',
                                attributes: { exclude: ["createdAt", "updatedAt", "sort", "status"] },

                            }

                        ]
                    }

                ]
            },
            {
                model: db.OrderDetail, as: 'orderDetails',
                attributes: { exclude: ["qty", "orderId", "createdAt", "updatedAt"] },

            },
            {
                model: db.OrderTracking,
                as: 'orderTrackings',
                attributes: { exclude: ["updatedAt", "createdAt"] }
            },
        ]
    })

    if (!orderDetailTb) {
        return res.error("Order not found!")
    }

    let hourLabel;
    let languageLabel;

    switch (userTb.language) {
        case 'KH':
            hourLabel = 'ម៉ោង'; // Khmer
            languageLabel = 'KHMER Language';
            break;
        case 'ZH':
            hourLabel = '小时'; // Chinese
            languageLabel = 'CHINESE Language';
            break;
        case 'EN':
        default:
            hourLabel = 'hour'; // Default to English
            languageLabel = 'ENGLISH Language';
    }

    const message = `
            ⚠️ *CANCELLATION REQUESTED* ⚠️
    
    👤 *Customer Information*
            *Name:* ${orderDetailTb.customerFirstName} ${orderDetailTb.customerLastName}  
            *Phone:* ${formatPhoneNumber(orderDetailTb.customerPhone)}  
            *Email:* ${orderDetailTb.customerEmail}
            *Language:* ${languageLabel}
            *Note:* ${orderDetailTb.note}
    
    🛠️ *Service Information*
            *Category Name:* ${orderDetailTb.productOptionV2?.product?.category?.nameEn}  
            *Product Name:* ${orderDetailTb.productOptionV2?.product?.nameEn}  
            *Product Option:* ${orderDetailTb.productOptionV2?.nameEn}  
 
            *Coupon Applied:* ${orderDetailTb.couponCode} 
            *Discount:* $${orderDetailTb.discount.toFixed(2)}
            *Service Fee:* $${orderDetailTb.serviceFee.toFixed(2)}
            *Transport Fee:* $${orderDetailTb.transportFee.toFixed(2)}
            *VAT:* $${orderDetailTb.vatFee.toFixed(2)}
            *Total Fee:* $${orderDetailTb.totalAmount.toFixed(2)}
            *Payment Method:* ${orderDetailTb.paymentMethod}
            *Payment Status:* ${orderDetailTb.paymentStatus}
             
    📍 *Location Details*
            *Address:* ${orderDetailTb.address}  
            *Floor:* ${orderDetailTb.floorNum}  
            *Room:* ${orderDetailTb.roomNum}  
    
    📅 *Schedule*
            *Start Date:* ${moment(orderDetailTb.scheduleStartDate).format('DD-MM-YYYY')}
            *Start Time:* ${moment(orderDetailTb.scheduleStartDate).format('h:mm A')}
            *End Time:* ${moment(orderDetailTb.scheduleStartDate).add(orderDetailTb.productOptionV2?.duration, 'hours').format('h:mm A')}
        `;

    if (['PENDING', 'ACCEPTED'].includes(orderDetailTb.status)) {
        orderDetailTb.status = 'CANCELLED'
        await orderDetailTb.save()
        await orderDetailTb.reload()
        alertOrderToAdmin(message);
    } else {
        return res.error("Order can not be cancelled")
    }

    await cancelPreAuthApi(orderDetailTb.tranId)

    await db.Order.update({ status: "CANCELLED", paymentStatus: "REFUNDED" }, { where: { userId: decodedUser.id, bulkOrderId: orderDetailTb.bulkOrderId }, })

    return res.success("Your order is cancelled")
}

module.exports = require("@asyncHandler")(cancelOrderV2)