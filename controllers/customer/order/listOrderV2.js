const db = require("@db")
const { Op } = require("sequelize")
const jwt = require('jsonwebtoken');
const alertOrderToAdmin = require("@services/telegram/alertOrderToAdmin")
const moment = require('moment');
const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';

const listOrder = async (req, res, next) => {

  const authHeader = req.headers['authorization'];
  const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

  if (!token) {
    return res.status(401).json({ message: 'Token not provided' });
  }

  const decodedUser = jwt.decode(token);

  let strSql = { userId: decodedUser.id }

  if (req.query.hasOwnProperty("status") && req.query.status != "null") {
    strSql = { ...strSql, status: req.query.status }
  }

  if (req.query.hasOwnProperty("paymentStatus") && req.query.paymentStatus != "null") {
    let status = "";

    switch (req.query.paymentStatus) {
      case 'PENDING':
        status = 'PENDING';
        break;
      case 'IN-REVIEW':
        status = 'IN-PROGRESS';
        break;
      case 'FAILED':
      case 'FAILED':
        status = 'CANCELLED';
        break;
      case 'PAID':
        status = 'COMPLETED';
        break;

      default:
        status = 'PENDING';
        break;
    }

    strSql = { ...strSql, status: status}
  }


  let orders = await db.Order.findAll({
    where: strSql,
    order: [["id", "DESC"]],
    attributes: ["id", "bulkOrderId", "thumbnailUrl", "isPrimary", "paymentStatus", "status", "discount", "serviceFee", "transportFee", "vat", "amount", "createdAt"],
    raw: true,
  })

  orders = groupByBulkOrderId(orders)

  orders = addBulkOrderAmountField(orders)



  return res.success(orders)


}

function groupByBulkOrderId(data) {
  const grouped = {};

  for (const order of data) {
    const key = order.bulkOrderId;

    if (!grouped[key]) {
      grouped[key] = [];
    }

    order.createdAt = moment(order.createdAt).format("DD-MM-YYYY HH:mm A")
    const vatFee = order.vat * (order.amount + order.serviceFee + order.transportFee - order.discount)
    order.totalPayableAmount = (order.amount + order.serviceFee + order.transportFee - order.discount + vatFee)
    grouped[key].push({
      id: order.id,
      thumbnailUrl: order.thumbnailUrl,
      bulkOrderId: order.bulkOrderId,
      isPrimary: order.isPrimary,
      paymentStatus: order.paymentStatus,
      status: order.status,
      // amount: order.amount,
      // discount: order.discount,
      // serviceFee: order.serviceFee,
      // transportFee: order.transportFee,
      totalPayableAmount: order.totalPayableAmount,
      totalPayableAmountDisplay: `$ ${order.totalPayableAmount.toFixed(2)}`,
      createdAt: order.createdAt
    });
  }

  return grouped;
}

function addBulkOrderAmountField(groupedOrders) {
  let orderList = []
  for (const bulkOrderId in groupedOrders) {

    let order = { bulkOrderId, id: 0, thumbnailUrl: '', amount: 0, amountDisplay: "$ 0.00", status: "PENDING", items: [], itemCount: 0 }
    const group = groupedOrders[bulkOrderId];

    // Sum totalPayableAmount for this group
    const bulkOrderAmount = group.reduce((sum, item) => sum + (item.totalPayableAmount || 0), 0);

    // Add the bulkOrderAmount to each item in the group
    for (const item of group) {
      if (item.isPrimary == true) {
        order.id = item.id
      }
      order.status = item.status
      order.items.push(item)
      order.thumbnailUrl = item.thumbnailUrl
        ? `${STORAGE_BASE_URL}/images/icons/${item.thumbnailUrl}`
        : null;
    }


    order.amount = bulkOrderAmount
    order.amountDisplay = `$ ${bulkOrderAmount.toFixed(2)}`
    order.amount = bulkOrderAmount
    order.itemCount = order.items.length
    order.createdAt = order.items[0].createdAt
    // order.items = undefined

    orderList.push(order)
  }

  return orderList;
}


module.exports = require("@asyncHandler")(listOrder)