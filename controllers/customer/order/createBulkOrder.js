const db = require("@db")
const { Op } = require("sequelize")
const jwt = require('jsonwebtoken');
const moment = require('moment');
const alertOrderToAdmin = require("@services/telegram/alertOrderToAdmin")

const checkoutBulkPreauthService = require("@services/payway/checkoutBulkPreauth")

const createOrderService = require("@services/order/createOrder")

const createBulkOrder = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })

    const paymentMethod = req.body.paymentMethod
    const receiptFile = req.body.receiptFile

    const productOptionId = req.body.productOptionId
    const productOptionIds = req.body.productOptionIds
    productOptionIds.push(productOptionId)


    const productAddOnIdList = Array.isArray(req.body.productAddOnIdList)

        ? req.body.productAddOnIdList
        : [];

    const productAddOnIds = productAddOnIdList.map(p => p.id)

    const couponCode = req.body.couponCode
    const addressId = req.body.addressId
    const address = req.body.address
    const floorNum = req.body.floorNum
    const roomNum = req.body.roomNum
    const scheduleStartDate = req.body.scheduleStartDate
    const note = req.body.note



        const couponTb = await db.Coupon.findOne({ where: { code: req.body.couponCode } })
    
        if (couponTb) {
        if (couponTb.status == false) {
            return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
        }
    
        const now = new Date();
    
        if (couponTb.effectiveDate && now < new Date(couponTb.effectiveDate)) {
            return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
        }
    
        if (couponTb.expiredDate && now > new Date(couponTb.expiredDate)) {
            return res.error("This coupon has expired", res.HttpStatus.BAD_REQUEST);
        }
        }
    
     

        
    if (paymentMethod == "cash") {
        return res.error("Payment option is not supported")
    }

    let startTime = moment();
    console.log(`ooooooooooo Start Query [BulkOrderId] ooooooooooo`);
    const [results] = await db.sequelize.query(
        `SELECT id, bulk_order_id 
            FROM bulk_order_ids 
            WHERE is_used = 0 
                AND id >= FLOOR(RAND() * (SELECT MAX(id) FROM bulk_order_ids)) 
            ORDER BY id 
            LIMIT 1;`,
        { type: db.sequelize.QueryTypes.SELECT }
    );

    let endTime = moment();

    let diffInSeconds = endTime.diff(startTime, 'seconds');
    console.log(`ooooooooooo Finished Query [BulkOrderId] = Difference is ${diffInSeconds} seconds ooooooooooo`);

    startTime = moment();

    await db.BulkOrderId.update({ isUsed: true }, { where: { id: results.id } })
    endTime = moment();
    diffInSeconds = endTime.diff(startTime, 'seconds');
    console.log(`ooooooooooo Finished Update [BulkOrderId] status = Difference is ${diffInSeconds} seconds ooooooooooo`);

    const bulkOrderId = results.bulk_order_id; // ok
    const orderList = [];

    const productOptionTbs = await db.ProductOptionV2.findAll({
        where:
        {
            id: {
                [Op.in]: productOptionIds
            }
        },
        attributes: ["id"],
        include: [
            {
                as: 'product',
                model: db.Product,
                attributes: ["id"],
                include: [
                    {
                        as: 'category',
                        model: db.Category,
                        attributes: ["id"],
                        include: [
                            {
                                as: 'productAddOns',
                                model: db.ProductAddOnV2,
                                attributes: ["id"],
                            }
                        ]
                    }
                ]
            }
        ]
    })

    if (productOptionTbs.length == 0) {
        return res.error("Product Option not found!")
    }

    let products = []
    let categoryIds = []

    for (let productOption of productOptionTbs) {
        let item = { productOptionId: productOption.id, productAddOnIds: [] }

        if (productOptionId == productOption.id) {
            for (let productAddOn of productOption.product.category.productAddOns) {

                if (productAddOnIds.includes(productAddOn.id)) {
                    let proAddOn = productAddOnIdList.find((p) => {
                        if (p.id == productAddOn.id) {
                            return p;
                        }

                    })

                    item.productAddOnIds.push(proAddOn)
                }
            }
        }
        categoryIds.push(productOption.product.category.id)
        products.push(item)
    }

    let totalAmount = 0;
    let serviceFee = 0;
    let transportFee = 0;

    for (let product of products) {

        if (product.productOptionId == productOptionId) {
            serviceFee = parseFloat(process.env.SERVICE_FEE)
            transportFee = parseFloat(process.env.TRANSPORT_FEE)
        }

        let resp = await createOrderService(
            bulkOrderId,
            userTb,
            product.productOptionId,
            product.productAddOnIds,
            serviceFee,
            transportFee,
            couponCode,
            paymentMethod,
            {
                addressId,
                address,
                floorNum,
                roomNum,
                scheduleStartDate,
                note
            }
        );
        
        let subTotal = (resp.amount + resp.serviceFee + resp.transportFee - resp.discount)
        totalAmount = totalAmount + (subTotal * resp.vat) + subTotal
        orderList.push(resp)

    }


    if (orderList.length == 0) {
        return res.error("Order is not found!")
    }

    if (paymentMethod == "beasy_point") {

        if (userTb.balance < totalAmount) {
            return res.error("Your bEasy point is insufficient!")

        } else {
            const balance = userTb.balance - totalAmount
            await db.User.update({ balance }, { where: { id: userTb.id } })
            await db.Order.update({ paymentStatus: "PAID" }, { where: { bulkOrderId } })

        }

        return res.success({
            bulkOrderId,
            orderId: orderList[0].id,
            paymentResp: {
                currentBalance: `${userTb.balance.toFixed(2)}`,
                deductCredit: `${totalAmount.toFixed(2)}`
            }
        })
    }

    let paymentResp = await checkoutBulkPreauthService(bulkOrderId, paymentMethod)

    
    switch (paymentMethod) {

        case "cards":
            return res.success({
                bulkOrderId,
                orderId: orderList[0].id,
                paymentResp: {
                    ...paymentResp,
                    checkoutUrl: paymentResp.checkoutUrl
                },
            })

        case "ababank_transfef":
            return res.success({
                ...orderDetailTb.dataValues,
                bulkOrderId,
                orderId: orderList[0].id,
                paymentResp: { ...paymentResp, checkoutUrl: paymentResp.checkout_qr_url },
            })

        default:
            return res.success({
                bulkOrderId,
                orderId: orderList[0].id,
                paymentResp: { ...paymentResp, checkoutUrl: paymentResp.checkout_qr_url },
            })

    }



}

module.exports = require("@asyncHandler")(createBulkOrder)