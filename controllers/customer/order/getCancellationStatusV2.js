const db = require("@db")

const getCancellationStatusV2 = async (req, res, next) => {

    const bulkOrderId = req.params.id

    const orderTb = await db.Order.findOne({ where: { bulkOrderId: bulkOrderId } })


    if (!orderTb) {
        return res.error("Order not found!")
    }

    switch (orderTb.status) {
        case 'PENDING': return res.success({message: "Order can be cancelled", status: orderTb.status, cancellationCode: 1});
        case 'ACCEPTED':  return res.success({message: "Order can be cancelled",status: orderTb.status, cancellationCode: 2});
        case 'IN-PROGRESS':  return res.success({message: "Order can't be cancelled",status: orderTb.status, cancellationCode: 3});
        case 'COMPLETED':  return res.success({message: "Order can't be cancelled", status: orderTb.status, cancellationCode: 4});
        case 'CANCELLED':  return res.success({message: "Order can't be cancelled", status: orderTb.status,cancellationCode: 5});
        case 'REJECTED':  return res.success({message: "Order can't be cancelled", status: orderTb.status,cancellationCode: 6});
    }
}

module.exports = require("@asyncHandler")(getCancellationStatusV2)