const db = require("@db")

const getOrderPaymentStatusByTranId = async (req, res, next) => {

    const paymentStatus = await db.Order.findOne({ where: { tranId: req.params.tranId }, attributes: ["id", "status", "paymentStatus"], })

    if (paymentStatus) {
        return res.success(paymentStatus)
    } else {
        return res.error("Order not found!")
    }

}

module.exports = require("@asyncHandler")(getOrderPaymentStatusByTranId)