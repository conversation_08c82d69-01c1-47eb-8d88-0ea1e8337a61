const db = require("@db")
const jwt = require('jsonwebtoken');
const { Op } = require("sequelize")

const getOrderCustomerInfoService = require("@services/order/getOrderCustomerInfo")
const getBulkOrderPreviewService = require("@services/order/getOrderPreview")

const serviceFee = parseFloat(process.env.SERVICE_FEE)
const transportFee = parseFloat(process.env.TRANSPORT_FEE)

const applyCouponBulkOrder = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);

    const couponTb = await db.Coupon.findOne({ where: { code: req.body.couponCode } })

    if (!couponTb) {
        return res.error("This coupon is invalid", res.HttpStatus.BAD_REQUEST);
    }

    if (couponTb.status == false) {
        return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
    }

    const now = new Date();

    if (couponTb.effectiveDate && now < new Date(couponTb.effectiveDate)) {
        return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
    }

    if (couponTb.expiredDate && now > new Date(couponTb.expiredDate)) {
        return res.error("This coupon has expired", res.HttpStatus.BAD_REQUEST);
    }

    let { productOptionId, productOptionIds, productAddOnIdList, addressId, scheduleStartDate, paymentMethod, note } = req.body

    productOptionIds.push(productOptionId);

    productAddOnIdList = Array.isArray(productAddOnIdList)
        ? req.body.productAddOnIdList
        : [];

    const productAddOnIds = productAddOnIdList.map(p => p.id)

    const productOptionTbs = await db.ProductOptionV2.findAll({
        where:
        {
            id: {
                [Op.in]: productOptionIds
            }
        },
        attributes: ["id"],
        include: [
            {
                as: 'product',
                model: db.Product,
                attributes: ["id"],
                include: [
                    {
                        as: 'category',
                        model: db.Category,
                        attributes: ["id"],
                        include: [
                            {
                                as: 'productAddOns',
                                model: db.ProductAddOnV2,
                                attributes: ["id"],
                            }
                        ]
                    }
                ]
            }
        ]
    })

    let products = []


    for (let productOption of productOptionTbs) {
        
        console.log(" productOption ", productOption)

        if (productOption == undefined) {
            throw Error("Product Option is not found!")
        }

        let item = { productOptionId: productOption.id, productAddOnIds: [] }

        if (productOptionId == productOption.id) {

            for (let productAddOn of productOption.product.category.productAddOns) {

                if (productAddOnIds.includes(productAddOn.id)) {
                    item.productAddOnIds.push(productAddOn.id)
                }
            }
        }

        products.push(item)
    }

    const custInfo = await getOrderCustomerInfoService(decodedUser.id, addressId);

    let orderPreviewList = []
    let amount = 0

    for (const product of products) {
        const orderPreview = await getBulkOrderPreviewService(
            product.productOptionId,
            product.productOptionId == productOptionId ? productAddOnIdList : [],
            scheduleStartDate,
            custInfo.language
        );
        orderPreviewList.push(orderPreview)
        amount = amount + orderPreview.amount
 
    }
    
    const discount = couponTb.type == "PERCENTAGE" ? amount * couponTb.value : couponTb.value;

    const totalAmount = amount + transportFee + serviceFee - discount
    const vatFee = totalAmount * 0.1
    const totalPayableAmount = totalAmount + vatFee

    const resp = {
        ...custInfo,
        couponCode: req.body.couponCode,
        amount: `$${amount.toFixed(2)}`,
        discount: `($${discount.toFixed(2)})`,
        totalAmount: `$${totalAmount.toFixed(2)}`,
        serviceFee: `$${serviceFee.toFixed(2)}`,
        transportFee: `$${transportFee.toFixed(2)}`,
        vatFee: `$${vatFee.toFixed(2)}`,
        totalPayableAmount: `$${totalPayableAmount.toFixed(2)}`,
        paymentMethod,
        scheduleStartDate,
        note,
        orderPreviewList
    }

    return res.success(resp)
}

module.exports = require("@asyncHandler")(applyCouponBulkOrder)