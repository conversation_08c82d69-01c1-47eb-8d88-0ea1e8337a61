const db = require("@db")
const jwt = require('jsonwebtoken');
const moment = require("moment/moment");

const STORAGE_BASE_URL = process.env.STORAGE_BASE_URL || 'http://localhost';

const getOrderDetailV2 = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })
    const bulkOrderId = req.params.id

    const orderTbs = await db.Order.findAll({
        attributes: { exclude: ["userId", "productOptionIdV2", "updatedAt"] },
        where: { userId: decodedUser.id, bulkOrderId },
        include: [
            {
                model: db.ProductOptionV2, as: 'productOptionV2',
                attributes: { exclude: ["createdAt", "updatedAt", "sort", "productId"] },
                include: [
                    {
                        model: db.Product, as: 'product',
                        attributes: { exclude: ["createdAt", "updatedAt", "categoryId", "sort", "status", "amount", "taskInfoEn", "taskInfoKm", "taskInfoVi", "taskInfoCn", "taskInfoTw"] },
                        include: [
                            {
                                model: db.Category, as: 'category',
                                attributes: { exclude: ["createdAt", "updatedAt", "sort", "status", "taskInfoEn", "taskInfoKm", "taskInfoVi", "taskInfoCn", "taskInfoTw"] },

                            }

                        ]
                    }

                ]
            },
            {
                model: db.OrderDetail, as: 'orderDetails',
                attributes: { exclude: ["orderId", "createdAt", "updatedAt"] }
            },
            {
                model: db.OrderTracking,
                as: 'orderTrackings',
                attributes: { exclude: ["updatedAt", "createdAt"] }
            },
            {
                model: db.UserAddress,
                as: 'userAddress',
                attributes: { exclude: ["updatedAt", "createdAt"] }
            },
        ]
    })

    if (orderTbs.length == 0) {
        return res.error("Order not found")
    }


    let hourLabel;

    switch (userTb.language) {
        case 'KH':
            hourLabel = 'ម៉ោង'; // Khmer
            break;
        case 'ZH':
            hourLabel = '小时'; // Chinese
            break;
        case 'EN':
        default:
            hourLabel = 'hour'; // Default to English
    }


    const resp = {

        bulkOrderId,
        fullname: "",
        phone: "",
        email: "",
        note: "",
        address: "",
        scheduleDate: "",
        couponCode: "",

        amount: 0.00,
        amountDisplay: 0.00,

        discount: 0.00,
        discountDisplay: "",

        serviceFee: 0.00,
        serviceFeeDisplay: "",

        transportFee: 0.00,
        transportFeeDisplay: "",

        subTotal: 0.00,
        subTotalDisplay: "",

        vatFee: 0.00,
        vatFeeDisplay: "",

        totalPayableAmount: 0.00,
        totalPayableAmountDisplay: "",
        paymentMethod: "",

        paymentStatus: "PENDING",
        status: "PENDING",
        items: []
    }

    let vat = 0

    for (let orderTb of orderTbs) {
        let order = orderTb.dataValues

        resp.fullname = `${order.customerFirstName} ${order.customerLastName}`
        resp.phone = order.customerPhone
        resp.email = order.customerEmail
        resp.note = order.note
        resp.address = order.address
        resp.scheduleDate = moment(order.scheduleStartDate).format("DD-MM-YYYY HH:mm A")
        resp.couponCode = order.couponCode
        resp.paymentMethod = order.paymentMethod

        resp.status = order.status
         resp.paymentStatus = order.paymentStatus

        if (order.isPrimary) {
            
        }

        resp.amount = resp.amount + order.amount
        resp.amountDisplay = `$ ${resp.amount.toFixed(2)}`

        resp.discount = resp.discount + order.discount
        resp.discountDisplay = `$ ${resp.discount.toFixed(2)}`

        resp.amountAfterDiscountDisplay = `$ ${(resp.amount - resp.discount).toFixed(2)}`

        let addOnAmount = 0
        for (let orderDetail of order.orderDetails) {
            addOnAmount = addOnAmount + (orderDetail.qty * orderDetail.amount)
        }

        resp.subTotal = resp.subTotal + (order.amount - order.discount + order.transportFee + order.serviceFee )
        resp.subTotalDisplay = `$ ${resp.subTotal.toFixed(2)}`
        resp.paymentMethodDisplay = order.paymentMethodDisplay

        if (order.serviceFee != 0) {
            resp.serviceFee = order.serviceFee
            resp.serviceFeeDisplay = `$ ${resp.serviceFee.toFixed(2)}`
        }

        if (order.transportFee != 0) {
            resp.transportFee = order.transportFee
            resp.transportFeeDisplay = `$ ${resp.transportFee.toFixed(2)}`
        }

        vat = order.vat

        let orderDetails = order.orderDetails.map(o => {
            return { ...o.dataValues, amountDisplay: `$ ${o.dataValues.amount.toFixed(2)}` }
        })



        let item = {
            categoryNameEn: order.productOptionV2?.product.category.nameEn,
            categoryNameKm: order.productOptionV2?.product.category.nameKm,
            categoryNameVi: order.productOptionV2?.product.category.nameVi,
            categoryNameCn: order.productOptionV2?.product.category.nameCn,
            categoryNameTw: order.productOptionV2?.product.category.nameTw,
            thumbnailUrl: `${STORAGE_BASE_URL}/images/icons/${order.thumbnailUrl}`,
            productOptionEn: order.productOptionV2?.nameEn,
            productOptionKm: order.productOptionV2?.nameKm,
            productOptionVi: order.productOptionV2?.nameVi,
            productOptionCn: order.productOptionV2?.nameCn,
            productOptionTw: order.productOptionV2?.nameTw,
            hourCount: order.hourCount,
            cleanerCount: order.cleanerCount,
            floorCount: order.floorCount,
            bedroomCount: order.bedroomCount,
            amount: order.amount,
            amountDisplay: `$ ${order.amount.toFixed(2)}`,
            addOns: orderDetails
        }
        resp.items.push(item)
    }

    resp.vatFee = parseFloat((resp.subTotal * vat).toFixed(2))
    resp.vatFeeDisplay = `$ ${resp.vatFee.toFixed(2)}`

    resp.totalPayableAmount = resp.vatFee + resp.subTotal
    resp.totalPayableAmountDisplay = `$ ${resp.totalPayableAmount.toFixed(2)}`

    // const paymentMethodTb = await db.PaymentMethod.findOne({where:{code: resp.paymentMethod}})
    // resp.paymentMethodDisplay = paymentMethodTb.nameEn

    return res.success(resp)
}

module.exports = require("@asyncHandler")(getOrderDetailV2)

