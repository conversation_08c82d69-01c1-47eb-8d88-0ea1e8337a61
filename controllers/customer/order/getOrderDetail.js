const db = require("@db")
const jwt = require('jsonwebtoken');

const getOrderDetail = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({where:{id: decodedUser.id}})

    const orderTb = await db.Order.findOne({
        attributes: { exclude: ["userId", "productOptionId", "updatedAt"] },
        where: { userId: decodedUser.id, id: req.params.id },
        include: [
            {
                model: db.ProductOption, as: 'productOption',
                attributes: { exclude: ["createdAt", "updatedAt", "sort", "productId"] },
                include: [
                    {
                        model: db.Product, as: 'product',
                        attributes: { exclude: ["createdAt", "updatedAt", "categoryId", "sort","status","amount"] },
                        include: [
                            {
                                model: db.Category, as: 'category',
                                attributes: { exclude: ["createdAt", "updatedAt", "sort","status"] },

                            }

                        ]
                    }

                ]
            },
            {
                model: db.OrderDetail, as: 'orderDetails',
                attributes: { exclude: ["qty","orderId","createdAt", "updatedAt"] },
            
            },
            {
                model: db.OrderTracking,
                as: 'orderTrackings',
                attributes: { exclude: ["updatedAt", "createdAt"] }
            },
               {
                model: db.UserAddress,
                as: 'userAddress',
                attributes: { exclude: ["updatedAt", "createdAt"] }
            },
        ]
    })

    if (!orderTb) {
        return res.error("Order not found")
    }


    let hourLabel;

    switch (userTb.language) {
    case 'KH':
        hourLabel = 'ម៉ោង'; // Khmer
        break;
    case 'ZH':
        hourLabel = '小时'; // Chinese
        break;
    case 'EN':
    default:
        hourLabel = 'hour'; // Default to English
    }

    orderTb.dataValues.duration = `${orderTb.productOption.duration} ${hourLabel}`
    
    return res.success(orderTb)
}

module.exports = require("@asyncHandler")(getOrderDetail)