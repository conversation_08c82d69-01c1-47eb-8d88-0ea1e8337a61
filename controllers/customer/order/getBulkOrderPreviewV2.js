
const jwt = require('jsonwebtoken');

const { Op } = require("sequelize")
const db = require("@db")

const serviceFee = parseFloat(process.env.SERVICE_FEE)
const transportFee = parseFloat(process.env.TRANSPORT_FEE)

const getBulkOrderPreviewService = require("@services/order/getOrderPreview")
const getOrderCustomerInfoService = require("@services/order/getOrderCustomerInfo")
const listCategoryService = require("@services/category/listCategory")


const getBulkOrderPreviewV2 = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }
    let { productOptionId, productOptionIds, productAddOnIdList, addressId, paymentMethod, scheduleStartDate, note } = req.body

    productOptionIds.push(productOptionId)

    productAddOnIdList = Array.isArray(productAddOnIdList)
        ? req.body.productAddOnIdList
        : [];

    const productAddOnIds = productAddOnIdList.map(p => p.id)


    const decodedUser = jwt.decode(token);


    const productOptionTbs = await db.ProductOptionV2.findAll({
        where:
        {
            id: {
                [Op.in]: productOptionIds
            }
        },
        attributes: ["id"],
        include: [
            {
                as: 'product',
                model: db.Product,
                attributes: ["id"],
                include: [
                    {
                        as: 'category',
                        model: db.Category,
                        attributes: ["id"],
                        include: [
                            {
                                as: 'productAddOns',
                                model: db.ProductAddOnV2,
                                attributes: ["id"],
                            }
                        ]
                    }
                ]
            }
        ]
    })

    let products = []
    let categoryIds = []
    for (let productOption of productOptionTbs) {
        let item = { productOptionId: productOption.id, productAddOnIds: [] }

        if (productOptionId == productOption.id) {
            for (let productAddOn of productOption.product.category.productAddOns) {
                if (productAddOnIds.includes(productAddOn.id)) {
                    item.productAddOnIds.push(productAddOn.id)
                }
            }
        }
        categoryIds.push(productOption.product.category.id)

        products.unshift(item)
    }

    const custInfo = await getOrderCustomerInfoService(decodedUser.id, addressId);

    let orderPreviewList = []
    let discount = 0
    let amount = 0



    for (const product of products) {

        const orderPreview = await getBulkOrderPreviewService(
            product.productOptionId,
            product.productOptionId == productOptionId ? productAddOnIdList : [],
            scheduleStartDate,
            custInfo.language
        );

        orderPreview['amountAfterDiscountDisplay'] = `$ ${(orderPreview.amount - orderPreview.discount).toFixed(2)}`
        orderPreviewList.push(orderPreview)

        amount = amount + (orderPreview.amount)

    }


    const totalAmount = amount + serviceFee + transportFee

    const vatFee = totalAmount * 0.1
    const totalPayableAmount = totalAmount + vatFee

    const categories = await listCategoryService({ ...req.query, status: 1 }, [
        "id",
        "nameEn", "nameKm", "nameVi", "nameCn", "nameTw",
        "iconUrl", "isComingSoon",
        "noteEn", "noteKm", "noteVi", "noteCn", "noteTw",
        "taskInfoEn", "taskInfoKm", "taskInfoCn", "taskInfoTw", "taskInfoVi"
    ])

    const recommendedCats = categories.filter(category => {
        if (categoryIds.includes(1) || categoryIds.includes(2)) {
            if (category.id == 9 || category.id == 13) {
                if (!categoryIds.includes(category.id)) {
                    return category;
                }

            }
        } else if (categoryIds.includes(7)) {
            if (category.id == 13) {
                if (!categoryIds.includes(category.id)) {
                    return category;
                }
            }
        }

    })

    const resp = {
        ...custInfo,
        amount: `$${amount.toFixed(2)}`,
        discount: `($${discount.toFixed(2)})`,
        totalAmount: `($${totalAmount.toFixed(2)})`,
        serviceFee: `$${serviceFee.toFixed(2)}`,
        transportFee: `$${transportFee.toFixed(2)}`,
        vatFee: `$${vatFee.toFixed(2)}`,
        totalPayableAmount: `$${totalPayableAmount.toFixed(2)}`,
        paymentMethod,
        scheduleStartDate,
        note,
        orderPreviewList,
        recommendedCats
    }

    return res.success(resp)
}

module.exports = require("@asyncHandler")(getBulkOrderPreviewV2)