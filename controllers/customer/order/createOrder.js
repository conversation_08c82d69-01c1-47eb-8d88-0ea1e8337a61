const db = require("@db")
const { Op } = require("sequelize")
const jwt = require('jsonwebtoken');
const serviceFee = parseFloat(process.env.SERVICE_FEE)
const transportFee = parseFloat(process.env.TRANSPORT_FEE)
const checkoutPreauthService = require("@services/payway/checkoutPreauth")
const checkoutRestService = require("@services/payway/checkoutRest")

const { randomUUID } = require('crypto');


const createOrder = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })
    const paymentMethod = req.body.paymentMethod
    const receiptFile = req.body.receiptFile
    const bulkOrderId = randomUUID(); // ok

    if (paymentMethod == "cash") {
        return res.error("Payment option is not supported")
    }

    const productOption = await db.ProductOption.findOne({
        where: { id: req.body.productOptionId },
        attributes: ["id", "amount"],
        raw: true
    })


    if (productOption.code == "cash") {
        return res.error("This payment option is not supported")
    }

    const productAddOn = await db.ProductAddOn.findAll({
        where: {
            id: {
                [Op.in]: req.body.productAddOnIds
            }
        },
        raw: true
    })

    let orderDetailPayload = productAddOn.map(pro => {
        return {
            productAddOnId: pro.id,
            nameEn: pro.nameEn,
            nameKm: pro.nameKm,
            nameVi: pro.nameVi,
            nameCn: pro.nameCn,
            nameTw: pro.nameTw,
            amount: pro.amount,
            qty: 1
        }
    })

    const addOnAmount = productAddOn.reduce((sum, addOn) => sum + (addOn.amount || 0), 0);

    // const amount = productOption.amount + addOnAmount
    // const discount = couponTb.type == "FIXED" ? couponTb.value  : amount * couponTb.value
    // const amountAfterDiscount =  amount - discount
    // const vatFee = (amountAfterDiscount + serviceFee) * 0.1
    // const totalPayableAmount = amountAfterDiscount + serviceFee + vatFee

    let orderPayload = {
        ...req.body,
        bulkOrderId,
        userId: decodedUser.id,
        customerFirstName: userTb.firstName,
        customerLastName: userTb.lastName,
        customerPhone: userTb.username,
        customerEmail: userTb.email,
        amount: productOption.amount + addOnAmount,
        serviceDuration: productOption.duration,
        serviceFee,
        transportFee
    }

    const customerAddress = await db.UserAddress.findOne({ where: { id: orderPayload.addressId || 0, userId: decodedUser.id } });

    if (customerAddress) {
        orderPayload = {
            ...orderPayload,
            address: customerAddress.address,
            floorNum: customerAddress.floorNum,
            roomNum: customerAddress.roomNum,
        }
    }

    if (req.body.couponCode) {
        const couponTb = await db.Coupon.findOne({ where: { code: req.body.couponCode } })

        if (!couponTb) {
            return res.error("This coupon is invalid", res.HttpStatus.BAD_REQUEST);
        }
        if (couponTb.status == false) {
            return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
        }
        const now = new Date();
        if (couponTb.effectiveDate && now < new Date(couponTb.effectiveDate)) {
            return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
        }

        if (couponTb.expiredDate && now > new Date(couponTb.expiredDate)) {
            return res.error("This coupon has expired", res.HttpStatus.BAD_REQUEST);
        }
        const amount = productOption.amount + addOnAmount
        const discount = couponTb.type == "FIXED" ? couponTb.value : amount * couponTb.value
        orderPayload = { ...orderPayload, discount }
    }

    const paymentMethodTb = await db.PaymentMethod.findOne({ where: { code: paymentMethod } })

    if (!paymentMethodTb) return res.error("Payment method not found!")

    orderPayload = {
        ...orderPayload,
        paymentMethodType: paymentMethodTb.type,
        paymentMethodDisplay: paymentMethodTb.nameEn,
        paymentStatus: paymentMethodTb.code == "ababank_transfer" && receiptFile != null ? "IN-REVIEW" : "PENDING"
    }

    const orderTb = await db.Order.create(orderPayload)

    orderDetailPayload = orderDetailPayload.map(o => {
      
        return { ...o, orderId: orderTb.id }
    })


    

    await db.OrderDetail.bulkCreate(orderDetailPayload);

    const orderDetailTb = await db.Order.findOne({
        attributes: { exclude: ["userId", "productOptionId", "updatedAt"] },
        where: { userId: decodedUser.id, id: orderTb.id },
        include: [
            {
                model: db.ProductOption, as: 'productOption',
                attributes: { exclude: ["createdAt", "updatedAt", "sort", "productId"] },
                include: [
                    {
                        model: db.Product, as: 'product',
                        attributes: { exclude: ["createdAt", "updatedAt", "categoryId", "sort", "status", "amount"] },
                        include: [
                            {
                                model: db.Category, as: 'category',
                                attributes: { exclude: ["createdAt", "updatedAt", "sort", "status"] },

                            }

                        ]
                    }

                ]
            },
            {
                model: db.OrderDetail, as: 'orderDetails',
                attributes: { exclude: ["qty", "orderId", "createdAt", "updatedAt"] },

            },
            {
                model: db.OrderTracking,
                as: 'orderTrackings',
                attributes: { exclude: ["updatedAt", "createdAt"] }
            },
        ]
    })
    
    let hourLabel;
    let languageLabel;

    if (orderTb.paymentMethod == "beasy_point") {
        if (userTb.balance < orderTb.totalAmount) {
            return res.error("Your bEasy point is insufficient!")
        } else {
            const balance = userTb.balance - (orderTb.totalAmount + orderTb.vatFee)
            await db.User.update({ balance }, { where: { id: userTb.id } })
            orderTb.paymentStatus = "PAID";
            await orderTb.save();
        }
    }

    switch (userTb.language) {
        case 'KH':
            hourLabel = 'ម៉ោង'; // Khmer
            languageLabel = 'KHMER Language';
            break;
        case 'ZH':
            hourLabel = '小时'; // Chinese
            languageLabel = 'CHINESE Language';
            break;
        case 'EN':
        default:
            hourLabel = 'hour'; // Default to English
            languageLabel = 'ENGLISH Language';
    }

    const addOnList = productAddOn
        .map((addOn, i) => `#${i + 1}-${addOn.nameEn}`)
        .join(' , ');


    orderDetailTb.dataValues.duration = `${orderDetailTb.productOption.duration} ${hourLabel}`
    // let paymentResp = await checkoutRestService(orderDetailTb.id, orderDetailTb.paymentMethod)
    let paymentResp = await checkoutPreauthService(orderDetailTb.id, orderDetailTb.paymentMethod)

    switch (paymentMethod) {


        case "cards":
            return res.success({
                ...orderDetailTb.dataValues,
                receiptFile: orderDetailTb.receiptFile,
                paymentResp: { ...paymentResp, checkoutUrl: paymentResp.checkoutUrl },
            })

        case "ababank_transfef":
            return res.success({
                ...orderDetailTb.dataValues,
                receiptFile: orderDetailTb.receiptFile,
                paymentResp: { ...paymentResp, checkoutUrl: paymentResp.checkout_qr_url },
            })

        case "beasy_point":
            return res.success({
                ...orderDetailTb.dataValues,
                receiptFile: orderDetailTb.receiptFile,
                paymentResp: {
                    currentBalance: `${userTb.balance.toFixed(2)}`,
                    deductCredit: `${(orderTb.totalAmount + orderTb.vatFee).toFixed(2)}`
                }
            })

        default:
            return res.success({
                ...orderDetailTb.dataValues,
                receiptFile: orderDetailTb.receiptFile,
                paymentResp: { ...paymentResp, checkoutUrl: paymentResp.checkout_qr_url },
            })

    }

}

module.exports = require("@asyncHandler")(createOrder)