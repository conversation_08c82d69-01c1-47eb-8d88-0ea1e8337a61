const db = require("@db")
const jwt = require('jsonwebtoken');

const getOrderCustomerInfoService = require("@services/order/getOrderCustomerInfo")
const getBulkOrderPreviewService = require("@services/order/getOrderPreview")
const serviceFee = parseFloat(process.env.SERVICE_FEE)
const transportFee = parseFloat(process.env.TRANSPORT_FEE)

const applyCouponBulkOrder = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);

    const couponTb = await db.Coupon.findOne({ where: { code: req.body.couponCode } })

    if (!couponTb) {
        return res.error("This coupon is invalid", res.HttpStatus.BAD_REQUEST);
    }
    if (couponTb.status == false) {
        return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
    }
    const now = new Date();
    if (couponTb.effectiveDate && now < new Date(couponTb.effectiveDate)) {
        return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
    }

    if (couponTb.expiredDate && now > new Date(couponTb.expiredDate)) {
        return res.error("This coupon has expired", res.HttpStatus.BAD_REQUEST);
    }


    const {products , addressId, scheduleStartDate , paymentMethod, note} = req.body

    const custInfo = await getOrderCustomerInfoService(decodedUser.id, addressId);

    let orderPreviewList = []
    let discount = 0
    let amount = 0

    for (const product of products) {

        const orderPreview = await getBulkOrderPreviewService(
            product.productOptionId,
            product.productAddOnIds || [],
            scheduleStartDate,
            custInfo.language
        );
        orderPreviewList.push(orderPreview)
        amount = amount + orderPreview.amount
    }

    const totalAmount = amount + transportFee + serviceFee
    const vatFee = totalAmount * 0.1
    const totalPayableAmount = totalAmount + vatFee

    const resp = {
        ...custInfo,
        amount: `$${amount.toFixed(2)}`,
        discount: `($${discount.toFixed(2)})`,
        totalAmount: `($${totalAmount.toFixed(2)})`,
        serviceFee: `$${serviceFee.toFixed(2)}`,
        transportFee: `$${transportFee.toFixed(2)}`,
        vatFee: `$${vatFee.toFixed(2)}`,
        totalPayableAmount: `$${totalPayableAmount.toFixed(2)}`,
        paymentMethod,
        scheduleStartDate,
        note,
        orderPreviewList
    }

    return res.success(resp)
}

module.exports = require("@asyncHandler")(applyCouponBulkOrder)