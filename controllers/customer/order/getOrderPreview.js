const db = require("@db")
const { Op } = require("sequelize")
const jwt = require('jsonwebtoken');
const alertOrderToAdmin = require("@services/telegram/alertOrderToAdmin")
const moment = require('moment');

const serviceFee = parseFloat(process.env.SERVICE_FEE)
const transportFee = parseFloat(process.env.TRANSPORT_FEE)

const getOrderPreview = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })

    let userAddressTb;

    if (req.body.addressId) {
        userAddressTb = await db.UserAddress.findOne({
            where: { id: req.body.addressId },
            attributes:
                [
                    "name",
                    "address",
                    "addressDetail",
                    "floorNum",
                    "roomNum",
                    "note"
                ]
        })
    }

    const productOption = await db.ProductOption.findOne({
        where: { id: req.body.productOptionId },
        attributes: ["id", "amount", "nameEn", "nameKm", "nameCn", "nameTw", "nameVi", 'duration'],
        include: [
            {
                model: db.Product, as: "product",
                attributes: ["nameEn", "nameKm", "nameVi" , "nameCn", "nameTw"],
                include: [
                    {
                        model: db.Category, as: "category",
                        attributes: ["nameEn", "nameKm", "nameVi", "nameCn", "nameTw" , "iconUrl"],
                    }
                ]
            },

        ],

    })

    const productAddOn = await db.ProductAddOn.findAll({
        attributes: ["nameEn", "nameKm",  "nameVi", "nameCn", "nameTw", "amount"],
        where: {
            id: {
                [Op.in]: req.body.productAddOnIds
            }
        },
        raw: true
    })  
    

    const addOnAmount = productAddOn.reduce((sum, addOn) => sum + ( addOn.amount || 0 ), 0);
    
    const discount = 0
 
    const amount = productOption.amount 
    const totalAmount = amount + serviceFee + transportFee
    const vatFee = totalAmount * 0.1
    const totalPayableAmount = totalAmount + vatFee



    let hourLabel;

    switch (userTb.language) {
        case 'KH':
            hourLabel = 'ម៉ោង'; // Khmer
            break;
        case 'ZH':
            hourLabel = '小时'; // Chinese
            break;
        case 'EN':
        default:
            hourLabel = 'hour'; // Default to English
    }

    const orderPayload = {
        ...req.body,
        address: userAddressTb?.dataValues.address,
        floorNum: userAddressTb?.dataValues.floorNum,
        roomNum: userAddressTb?.dataValues.roomNum,
        userId: decodedUser.id,
        customerFirstName: userTb.firstName,
        customerLastName: userTb.lastName,
        customerPhone: userTb.username,
        customerEmail: userTb.email,
        productAddOn,
        productOption,
        startDate: `${moment(req.body.scheduleStartDate).format('DD-MM-YYYY')} `,
        startTime: `${moment(req.body.scheduleStartDate).format('h:mm A')} `,
        endTime: `${moment(req.body.scheduleStartDate).add(productOption.duration, 'hours').format('h:mm A')} `,
        duration: `${productOption.duration} ${hourLabel} `,
        
        amount: `$${amount.toFixed(2)}`,
        discount: `($${discount.toFixed(2)})`,
        totalAmount: `($${totalAmount.toFixed(2)})`,
        serviceFee: `$${serviceFee.toFixed(2)}`,
        transportFee: `$${transportFee.toFixed(2)}`,
        vatFee: `$${vatFee.toFixed(2)}`,
        totalPayableAmount: `$${totalPayableAmount.toFixed(2)}`
    }

    return res.success(orderPayload)
}

module.exports = require("@asyncHandler")(getOrderPreview)