const db = require("@db")
const { Op } = require("sequelize")
const jwt = require('jsonwebtoken');

const serviceFee = parseFloat(process.env.SERVICE_FEE)
const transportFee = parseFloat(process.env.TRANSPORT_FEE)

const applyCoupon = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const couponTb = await db.Coupon.findOne({ where: { code: req.body.couponCode } })

    if (!couponTb) {
        return res.error("This coupon is invalid", res.HttpStatus.BAD_REQUEST);
    }
    if (couponTb.status == false) {
        return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
    }
    const now = new Date();
    if (couponTb.effectiveDate && now < new Date(couponTb.effectiveDate)) {
        return res.error("This coupon is not active yet", res.HttpStatus.BAD_REQUEST);
    }

    if (couponTb.expiredDate && now > new Date(couponTb.expiredDate)) {
        return res.error("This coupon has expired", res.HttpStatus.BAD_REQUEST);
    }

    const decodedUser = jwt.decode(token);
    const couponAlreadyUsed = await db.Order.findOne({
        where: {
            couponCode: req.body.couponCode,
            userId: decodedUser.id
        }
    });

    if (couponAlreadyUsed) {
        return res.error("You have already used this coupon", res.HttpStatus.BAD_REQUEST);
    }
    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })

    const productOption = await db.ProductOption.findOne({
        where: { id: req.body.productOptionId },
        attributes: ["id", "amount", "nameEn", "nameKm", "nameVi", "nameCn", "nameTw", "amount", 'duration'],
        include: [
            {
                model: db.Product, as: "product",
                attributes: ["nameEn", "nameKm", "nameVi", "nameCn", "nameTw"],
                include: [
                    {
                        model: db.Category, as: "category",
                        attributes: ["nameEn", "nameKm", "nameVi", "nameCn", "nameTw"],
                    }
                ]
            }
        ],

    })

    const productAddOn = await db.ProductAddOn.findAll({
        attributes: ["nameEn", "nameKm", "nameVi", "nameCn", "nameTw", "amount"],
        where: {
            id: {
                [Op.in]: req.body.productAddOnIds
            }
        },
        raw: true
    })



    const addOnAmount = productAddOn.reduce((sum, addOn) => sum + (addOn.amount || 0), 0);
    const amount = productOption.amount + addOnAmount
    const discount = couponTb.type == "FIXED" ? couponTb.value : amount * couponTb.value
    const totalAmount = amount - discount + serviceFee + transportFee
    const vatFee = totalAmount * 0.1
    const totalPayableAmount = totalAmount + vatFee

    const orderPayload = {
        ...req.body,
        couponCode: couponTb.code,
        promoText: couponTb.promoText,
        amount: `$${amount.toFixed(2)}`,
        discount: `($${discount.toFixed(2)})`,
        transportFee: `$${transportFee.toFixed(2)}`,
        serviceFee: `$${serviceFee.toFixed(2)}`,
        totalAmount: `$${totalAmount.toFixed(2)}`,
        vatFee: `$${vatFee.toFixed(2)}`,
        totalPayableAmount: `$${totalPayableAmount.toFixed(2)}`
    }

    return res.success(orderPayload)
}

module.exports = require("@asyncHandler")(applyCoupon)