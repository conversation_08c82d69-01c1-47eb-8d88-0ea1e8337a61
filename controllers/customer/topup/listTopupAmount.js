const db = require("@db")
const jwt = require('jsonwebtoken');
 
const listTopupAmount = async (req,res,next)=>{


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })


    return res.success({
        balance: `$${userTb.balance.toFixed(2)}`,
        amount: [ 20, 50, 80, 100, 120, 150, 180, 200, 250  ]
    })
}

module.exports = listTopupAmount