const db = require("@db")
const jwt = require('jsonwebtoken');
const { Op } = require("sequelize");

const getTopupDetail = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const topupId = req.params.id
    const decodedUser = jwt.decode(token);

    const topupTb = await db.Topup.findOne({

        where: {
            userId: decodedUser.id,
 
            id: topupId
        }
    })

    if(!topupTb){
        return res.error("Top up is not found!")
    }

    const paymentMethodTb = await db.PaymentMethod.findOne({ where: { code: topupTb.paymentMethod } })


    return res.success({
        ...topupTb.toJSON(),
        receiptFile: topupTb.receiptFile,
        paymentMethodDisplayEn: paymentMethodTb.nameEn,
        paymentMethodDisplayKm: paymentMethodTb.nameKm,
        paymentMethodDisplayCn: paymentMethodTb.nameCn,
        paymentMethodDisplayTw: paymentMethodTb.nameTw,
        paymentMethodDisplayVi: paymentMethodTb.nameVi,
        logo: paymentMethodTb.logoUrl
    })

}

module.exports = getTopupDetail
