const db = require("@db")
const { Op } = require("sequelize")
const jwt = require('jsonwebtoken');

const listTopup = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);

    const topupList = await db.Topup.findAll({
        where:
        {
            userId: decodedUser.id,
            status: { [Op.in]: ["IN-REVIEW", "FAILED", "PAID"] }
        },
        order:[["id","desc"]]
    })


    return res.success(topupList)

}

module.exports = listTopup
