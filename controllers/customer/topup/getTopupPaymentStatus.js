const db = require("@db")
const jwt = require('jsonwebtoken');

const getTopupPaymentStatus = async (req, res, next) => {


    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);
    const topupId = req.params.id;


    const topupTb = await db.Topup.findOne({
        attributes: ["id", "status"],
        where: { id: topupId, userId: decodedUser.id }
    })


    return res.success(topupTb)

}

module.exports = getTopupPaymentStatus
