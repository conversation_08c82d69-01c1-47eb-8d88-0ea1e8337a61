const db = require("@db")
const jwt = require('jsonwebtoken');

const { checkoutRestTopupAmount, checkoutRestTopupId } = require("@services/payway/checkoutRestTopup")

const createTopup = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({ where: { id: decodedUser.id } })

    const { topupId, topupAmount, paymentMethodCode, receiptFile } = req.body;

    if (["cash", "beasy_point"].includes(paymentMethodCode)) {
        return res.error("Payment option is not supported")
    }

    if (topupId == null && topupAmount == null) {
        return res.error("Topup amount is not valid")
    }

    let paymentResp = null;


    if (topupId != null) {
        paymentResp = await checkoutRestTopupId(paymentMethodCode, topupId, userTb.id, receiptFile);
    } else if (topupAmount != null) {
        paymentResp = await checkoutRestTopupAmount(paymentMethodCode, topupAmount, userTb.id, receiptFile);
    }

    if (typeof paymentResp !== "string" && paymentResp.hasOwnProperty("errorMessage")) {
        return res.error(paymentResp.errorMessage, res.HttpStatus.BAD_REQUEST, paymentResp)
    }

    switch (paymentMethodCode) {
        case "cards": return res.success(paymentResp)
        case "ababank_transfer":
            await db.Topup.update({ status: "IN-REVIEW", receiptFile, }, { where: { id: paymentResp.id } })
            return res.success(paymentResp)
        default: return res.success(paymentResp)
    }

}

module.exports = createTopup
