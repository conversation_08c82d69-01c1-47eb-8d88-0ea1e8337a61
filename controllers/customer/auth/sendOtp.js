const { Op } = require('sequelize');
const db = require("@db")
const moment = require("moment")
const sendOtpApi = require("@apis/plasgate/sendOtpApi")
const getClientIp = require("@utils/getClientIp")
const whitelistPhone = require('@constants/whitelistPhone')
const sendTestOtp = require('@services/telegram/sendTestOtp')
const normalizePhoneNumber = require("@apis/plasgate/normalizePhoneNumber")

const sendOtp = async (req, res, next) => {
    const { username, deviceId, lang} = req.body
 

    const formattedUsername = normalizePhoneNumber(username)

    // check if there is recent OTP associated with the phone

    const recentOtpCount = await db.UserOtp.count({
        where: {
            username: formattedUsername, // optional filter by phone
            expDate: {
                [Op.gt]: moment()  // must be in the future
            }
        }
    });
    // end recent OTP validation

    if (recentOtpCount > 0) {
        return res.error("You must wait 5 minute to request a new OTP")
    }

    const clientIp = getClientIp(req)

    const otp = Math.floor(100000 + Math.random() * 900000);

    await db.UserOtp.update({ status: false }, { where: { username: formattedUsername, isVerified: false } })

    await db.UserOtp.create({ username: formattedUsername, otp, clientIp })

    if (whitelistPhone.includes(formattedUsername)) {   
        sendTestOtp(formattedUsername,otp);
        return res.success(null, "Otp is sent to customer")
    }

    await sendOtpApi(formattedUsername, otp, lang)
    return res.success(null, "Otp is sent to customer")

}


module.exports = require("@asyncHandler")(sendOtp)