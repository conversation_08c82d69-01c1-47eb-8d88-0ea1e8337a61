const { Op } = require('sequelize');
const db = require("@db")
const moment = require("moment")
const sendOtpApi = require("@apis/plasgate/sendOtpApi")
const getClientIp = require("@utils/getClientIp")
const normalizePhoneNumber = require("@apis/plasgate/normalizePhoneNumber")

const createAccount = async (req, res, next) => {
    const { username, firstName, lastName, referralCode } = req.body

    const formattedUsername = normalizePhoneNumber(username)
    const formatteReferralCode = normalizePhoneNumber(referralCode)

    // check if there is existing account associated with the phone
    const existingAccountVerified = await db.User.count({ where: { username: formattedUsername, status: "ACTIVE" } })
    if (existingAccountVerified > 0) {
        return res.error("The phone number is already associated with an existing account", res.HttpStatus.UNAUTHORIZED);
    }

    const existingAccountUnverified = await db.User.findOne({ where: { username: formattedUsername, status: "PENDING" } })
    if (existingAccountUnverified) {
        return res.error("The phone number is already associated with an existing account", res.HttpStatus.UNAUTHORIZED);
    }

    const existingAccountSuspended = await db.User.findOne({ where: { username: formattedUsername, status: "SUSPENDED" } })
    if (existingAccountSuspended) {
        return res.error("The phone number is already associated with an existing suspended account", res.HttpStatus.UNAUTHORIZED);
    }
    // end account validation

    // verify if OTP is valid or not
    // const now = moment();
    // const fiveMinutesFromNow = moment().add(5, 'minutes');

    // const userOtp = await db.UserOtp.findOne({
    //     where: {
    //         username: formattedUsername,
    //         otp: otp,
    //         status: 1,
    //         isVerified: false, // assuming `verified` is a boolean column
    //         expDate: {
    //             [Op.gte]: now.toDate(),             // not expired yet
    //             [Op.lte]: fiveMinutesFromNow.toDate() // still within 5 minutes window
    //         }
    //     }
    // });
    // if (!userOtp) {
    //     return res.error("OTP is invalid", res.HttpStatus.UNAUTHORIZED);

    // }
    // userOtp.isVerified = true
    // await userOtp.save()
    // end validation of OTP

    const referrerTb = await db.User.findOne({ where: { username: formatteReferralCode } })

    // if(!referrerTb){
    //     return res.error("Referral code doesnt' exists")
    // }

    const userTb = await db.User.create({
        username: formattedUsername,
        firstName, lastName,
        status: "PENDING",
        referrerCode: referralCode,
    })


    const point = 2;

    if (referrerTb) {
        referrerTb.balance += point;
        await referrerTb.save();
        const referralPayload = {
            referrerCode: referrerTb.username,
            refereeCode: userTb.username,
            referralCode: referralCode,
            refereeId: userTb.id,
            referrerId: referrerTb.id,
            point
        }
        await db.UserReferral.create(referralPayload);

    }

    return res.success(userTb, "Account registeration is completed")

}


module.exports = require("@asyncHandler")(createAccount)