const { Op } = require('sequelize');
const db = require("@db")
const moment = require("moment")
const normalizePhoneNumber = require("@apis/plasgate/normalizePhoneNumber")

const checkAccountStatus = async (req, res, next) => {
    const username = req.params.username

    const formattedUsername = normalizePhoneNumber(username)
  const tmpUsername = formattedUsername.replace("855", "")
 console.log("tmpUsername " ,tmpUsername)
    // check if there is existing account associated with the phone
    const existingAccount = await db.User.count({
        where: {
            username: {
                [Op.like]: `%${tmpUsername}`
            }, 
            
            status: {
                [Op.in]: ["ACTIVE", "PENDING"]
            }
        }
    })
    if (existingAccount > 0) {
        return res.json({ status: true });
    }

    else {
        return res.json({ status: false });
    }

}


module.exports = require("@asyncHandler")(checkAccountStatus)