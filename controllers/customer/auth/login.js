const { Op } = require('sequelize');
const db = require("@db")
const jwt = require('jsonwebtoken');
const moment = require("moment")

const normalizePhoneNumber = require("@apis/plasgate/normalizePhoneNumber")

const login = async (req, res, next) => {
    const { username, otp } = req.body

    const formattedUsername = normalizePhoneNumber(username)

    const tmpUsername = formattedUsername.replace("855", "")
    console.log("tmpUsername", tmpUsername)
    
    // check if there is existing account associated with the phone
    const existingAccountVerified = await db.User.count({
        where: {
            username: {
                [Op.like]: `%${tmpUsername}`
            }, status: { [Op.in]: ["ACTIVE", "PENDING"] }
        }
    })
    if (existingAccountVerified == 0) {
        return res.error('Account not found!', res.HttpStatus.NOT_FOUND)
    }

    // fixed static OTP for appstore reviewer
    if (["**********", "***********", "***********"].includes(formattedUsername)) {
        const userTb = await db.User.findOne({
            where: {
                username: {
                    [Op.like]: `%${tmpUsername}`
                }
            }
        })
        const userInfo = { id: userTb.id, username: userTb.username }; // user payload
        const secret = process.env.JWT_SECRET; // use env in production
        const expiresIn = '30d'; // or '15m', '7d', etc.

        const token = jwt.sign(userInfo, secret, { expiresIn });

        return res.success({ token: token, userInfo: userTb }, "User account is active")
    }

    // end recent OTP validation

    // verify if OTP is valid or not

    const userOtp = await db.UserOtp.findOne({
        where: {
            username: {
                [Op.like]: `%${tmpUsername}`,
            },
            otp,
            status: true,
            isVerified: false, // assuming `verified` is a boolean column
            // expDate: {
            //     [Op.gte]: moment()  // must be in the future
            // }
        }
    });

    if (!userOtp) {
        return res.error("OTP is invalid", res.HttpStatus.UNAUTHORIZED)
    }

    userOtp.isVerified = true
    await userOtp.save()
    // end validation of OTP

    const userTb = await db.User.findOne({
        where: {
            username: {
                [Op.like]: `%${tmpUsername}`,
            },
        }
    })
    userTb.status = "ACTIVE";
    await userTb.save();

    const userInfo = { id: userTb.id, username: userTb.username }; // user payload
    const secret = process.env.JWT_SECRET; // use env in production
    const expiresIn = '30d'; // or '15m', '7d', etc.

    const token = jwt.sign(userInfo, secret, { expiresIn });

    return res.success({ token: token, userInfo: userTb }, "User account is active")

}


module.exports = require("@asyncHandler")(login)