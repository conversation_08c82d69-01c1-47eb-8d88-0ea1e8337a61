const jwt = require('jsonwebtoken');
const db = require("@db")

const getProductDetail = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>
    const productId = req.params.id

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const decodedUser = jwt.decode(token);

    const productTb = await db.Product.findOne({
        attributes: { exclude: ["createdAt", "updatedAt", "categoryId"] },
        where: { id: productId },
        include: [
            {
                as: 'category', model: db.Category,
                attributes: { exclude: ["createdAt", "updatedAt","sort","status"] }
            },
            {
                as: 'productOptions', model: db.ProductOption,
                attributes: { exclude: ["createdAt", "updatedAt", "productId","sort" ] },
                separate: true,
                order: [['sort', 'DESC']],
            },
             {
                as: 'productAddOns', model: db.ProductAddOn,
                attributes: { exclude: ["createdAt", "updatedAt", "productId","sort" , "status"] },
                separate: true,
                order: [['sort', 'DESC']],
            }
        ]
    })

    return res.success(productTb)
}

module.exports = require("@asyncHandler")(getProductDetail)
