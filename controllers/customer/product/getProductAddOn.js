const db = require("@db")
const { Op } = require('sequelize');

const getProductAddOns = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>
    const productOptionId = req.params.id

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const productOptionsTb = await db.ProductOption.findOne({
        attributes: ["id", "duration", "productId"],
        where: { id: productOptionId },
        raw: true
    })

    const productAddOnTb = await db.ProductAddOn.findAll({
        attributes: {
            exclude: ["createdAt", "updatedAt", "productId", "sort"]
        },
        where: {
            status: 1,
            productId: productOptionsTb.productId,
            duration: {
                [Op.lte]: productOptionsTb.duration
            }
        },
        order: [['sort', 'DESC']]
    });

    return res.success(productAddOnTb)
}

module.exports = require("@asyncHandler")(getProductAddOns)
