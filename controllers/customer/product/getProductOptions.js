const db = require("@db")
 
const getProductOptions = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>
    const productId = req.params.id

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const productOptionsTb = await db.ProductOption.findAll({
        attributes: { exclude: ["createdAt", "updatedAt", "productId","sort"] },
        where: { productId, status:1 },
        order: [['sort', 'DESC']]
    })
 
    return res.success(productOptionsTb)
}

module.exports = require("@asyncHandler")(getProductOptions)
