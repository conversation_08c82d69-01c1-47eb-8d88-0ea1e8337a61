const db = require("@db")

function compareVersions(v1, v2) {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);

    for (let i = 0; i < 3; i++) {
        const a = parts1[i] || 0;
        const b = parts2[i] || 0;

        if (a > b) return 1;
        if (a < b) return -1;
    }

    return 0;
}

const getAppUpdateStatus = async (req, res, next) => {
    const version = req.params.version;
    const phone = req.params.phone.toLowerCase();

    const appMetaInfo = await db.AppConfig.findOne();
    let isUpdateAvailable = false;
    switch (phone) {
        case 'ios':
            if (appMetaInfo.appstoreStatus === "RELEASED" && compareVersions(appMetaInfo.version, version) == 1) {
                isUpdateAvailable = true;

            }
            break;

        case 'android':
            if (appMetaInfo.playstoreStatus === "RELEASED" && compareVersions(appMetaInfo.version, version) == 1) {
                isUpdateAvailable = true;
            }
            break;

        case 'web':
            if (appMetaInfo.deploymentStatus === "RELEASED" && compareVersions(appMetaInfo.version, version) == 1) {
                isUpdateAvailable = true;
            }
            break;

        default: return res.error("Phone OS is invalid!")
    }

    return res.success({
        isUpdateAvailable,
        isForceUpdate: appMetaInfo.forceUpdate,
        latestVersion: appMetaInfo.version
    })
}

module.exports = require("@asyncHandler")(getAppUpdateStatus)