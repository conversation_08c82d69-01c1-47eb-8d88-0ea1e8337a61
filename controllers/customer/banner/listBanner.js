

const listBannerService = require("@services/banner/listBanner")


const listBanner = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.error('Token not provided', 401);
    }
    
    const resp = await listBannerService({ ...req.query, status: 1 },
        [
            "id", "name","hasDetail","hasTopup","hasBooking",
            "type", "imgUrlEn", "imgUrlKm", "imgUrlVi", "imgUrlCn","imgUrlTw",
            "titleEn", "titleVi", "titleKm", "titleCn", "titleTw", "contentEn", "contentVi", "contentCn","contentTw", "contentKm",
            "deeplink", "deeplinkType"

        ])

    return res.success(resp)

}

module.exports = require("@asyncHandler")(listBanner)