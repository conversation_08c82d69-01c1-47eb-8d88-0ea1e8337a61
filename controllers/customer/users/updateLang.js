const jwt = require('jsonwebtoken');
const db = require("@db")
 

const updateLang = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.error('Token not provided', res.HttpStatus.UNAUTHORIZED)
    }

    const { lang } = req.body

    const decodedUser = jwt.decode(token);
 

    await db.User.update({ language: lang }, { where: { id: decodedUser.id } })

    const updatedUserTb = await db.User.findOne({ where: { id: decodedUser.id } })

    return res.success(updatedUserTb)
}

module.exports = require("@asyncHandler")(updateLang)