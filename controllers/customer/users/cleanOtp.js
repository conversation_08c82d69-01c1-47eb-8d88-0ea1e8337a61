const jwt = require('jsonwebtoken');
const db = require("@db")
let whitelistPhone = require('@constants/whitelistPhone')

const cleanOtp = async (req, res, next) => {
    whitelistPhone = ['85517918508', '85593653893', '85586585891'];

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.error('Token not provided', res.HttpStatus.UNAUTHORIZED)
    }

    const decodedUser = jwt.decode(token);

    if (whitelistPhone.includes(decodedUser.username)) {
        const destroyCount = await db.UserOtp.destroy({ where: { username: decodedUser.username } })
        return res.success(`OTP ${destroyCount} records are deleted`)
    }

    return res.error(`You are not authorized to access this feature`, res.HttpStatus.UNAUTHORIZED)

}

module.exports = require("@asyncHandler")(cleanOtp)