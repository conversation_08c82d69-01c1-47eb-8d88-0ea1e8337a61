const jwt = require('jsonwebtoken');
const db = require("@db")

const uploadProfile = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.error('Token not provided', res.HttpStatus.UNAUTHORIZED)
    }

    const decodedUser = jwt.decode(token);
    const userTb = await db.User.findOne({ where: { id: decodedUser.id }, attributes: { exclude: ["createdAt"] } })

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }


    await db.User.update({ profileUrl: req.files?.profileFile?.[0]?.filename || null }, { where: { id: decodedUser.id } })

    await userTb.reload();

    return res.success(userTb)
}

module.exports = require("@asyncHandler")(uploadProfile)