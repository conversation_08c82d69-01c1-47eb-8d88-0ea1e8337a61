const jwt = require('jsonwebtoken');
const db = require("@db")

const getInfo = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.error('Token not provided', res.HttpStatus.UNAUTHORIZED)
    }


    const decodedUser = jwt.decode(token);

 

    const userTb = await db.User.findOne({
        where: { id: decodedUser.id },
        attributes: { exclude: ["createdAt"] },
        include: [
            {
                model: db.UserReferral, as: 'userReferrers',
                attributes: ["referralCode","refereeCode","referrerCode","point", "createdAt"],
            }
        ]

    })


    return res.success(userTb)
}

module.exports = require("@asyncHandler")(getInfo)