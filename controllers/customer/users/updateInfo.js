const jwt = require('jsonwebtoken');
const db = require("@db")
const { Op } = require('sequelize');


const updateInfo = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.error('Token not provided', res.HttpStatus.UNAUTHORIZED)
    }

    const { username, firstName, lastName, email } = req.body

    const decodedUser = jwt.decode(token);
    const hasExistingUser = await db.User.count({ where: { username, id: { [Op.ne]: decodedUser.id } } })
    if (hasExistingUser != 0) {
        return res.error('Username is already taken.', res.HttpStatus.BAD_REQUEST)
    }

    await db.User.update({ username, firstName, lastName, email }, { where: { id: decodedUser.id } })

    const updatedUserTb = await db.User.findOne({ where: { id: decodedUser.id } })

    return res.success(updatedUserTb)
}

module.exports = require("@asyncHandler")(updateInfo)