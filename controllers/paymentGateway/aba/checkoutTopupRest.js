const checkoutRestTopupId = require("@services/payway/checkoutRestTopupId");

const checkoutTopupRest = async (req, res, next) => {

  const { topupId, paymentMethod } = req.params
  
  const receiptFile = req.query.receiptFile
  
  console.log(" oooooooo checkoutTopupRest ooooooooo " , paymentMethod)

  const payment = await checkoutRestTopupId(topupId, paymentMethod, receiptFile)

  return res.success(payment)

};

module.exports = require("@asyncHandler")(checkoutTopupRest);