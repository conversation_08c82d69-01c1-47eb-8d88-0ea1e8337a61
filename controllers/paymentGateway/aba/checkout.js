const paymentCheckoutService = require("@services/payway/checkoutRest")
const baseUrl = process.env.ABA_BASE_URL;


const checkout = async (req, res, next) => {


  const { orderId, paymentMethod } = req.params

  const payment = await paymentCheckoutService(orderId, paymentMethod)

  console.log("payment")

  if (payment.hasOwnProperty('error')) {
    return res.error(
      payment.errorMessage,
      res.HttpStatus.BAD_REQEUST,
      {
        status: "error",
        stack: payment.stack,
      },
    );
  }

  const paywayPayload = {
    baseUrl,
    reqTime: payment.reqTime,
    merchantId: payment.merchantId,
    transactionId: payment.transactionId,
    phone: payment.phone,
    amount: payment.amount,
    paymentOption: payment.paymentOption,
    returnUrl: payment.returnUrl,
    successUrl: payment.successUrl,
    cancelUrl: "",
    hash: payment.hash,
    type:payment.type,
    abaScript: payment.abaScript,
    returnDeeplink: payment.returnDeeplink
  };

  return res.render('payment/aba.ejs', paywayPayload);

};

module.exports = checkout;
