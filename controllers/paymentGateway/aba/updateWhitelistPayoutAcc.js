
// const updateWhitelistPayoutAccApi = require("../../../api/payway/updateWhitelistPayoutAcc")

const updateWhitelistPayoutAcc = async (req, res) => {

    // try {
    //     const resp = await updateWhitelistPayoutAccApi(req.body.payoutAccount, req.body.status);

    //     return res.json(resp)
    // } catch (err) {
    //       console.log("updateWhitelistPayoutAcc catch error ", err)
    //     return res.json({ error: err.message })
    // }
}


module.exports = updateWhitelistPayoutAcc