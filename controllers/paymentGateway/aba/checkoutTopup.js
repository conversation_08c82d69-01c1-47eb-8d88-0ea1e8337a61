 
const checkoutRestTopupId = require("@services/payway/checkoutRestTopupId");
const baseUrl = process.env.ABA_BASE_URL;


const checkoutTopup = async (req, res, next) => {

    const { topupId, paymentMethod } = req.params
    const receiptFile = req.query.receiptFile
 
    const payment = await checkoutRestTopupId(topupId, paymentMethod, receiptFile)

    if (payment.hasOwnProperty('error')) {
        return res.error(
            payment.errorMessage,
            res.HttpStatus.BAD_REQEUST,
            {
                status: "error",
                stack: payment.stack,
            },
        );
    }

    console.log("oooooooo payment ooooooo", payment)

    const paywayPayload = {
        baseUrl,
        reqTime: payment.reqTime,
        merchantId: payment.merchantId,
        transactionId: payment.transactionId,
        phone: payment.phone,
        amount: payment.amount,
        paymentOption: payment.paymentOption,
        returnUrl: payment.returnUrl,
        successUrl: payment.successUrl,
        cancelUrl: "",
        hash: payment.hash,
        type:payment.type,
        returnDeeplink:payment.returnDeeplink,
        abaScript: payment.abaScript,
    };

    console.log("paywayPayload " , paywayPayload)

    return res.render('payment/aba.ejs', paywayPayload);

};

module.exports = require("@asyncHandler")(checkoutTopup);
