
// const completePreAuthApi = require("../../../api/payway/completePreAuthApi")
// const checkPaywayTranStatusApi = require('../../../api/payway/checkPaymentStatusApi')
// const fulfillmentMerchantId = process.env.ABA_FULFILLMENT_MERCHANT_ID
// const fulfillmentApiKey = process.env.ABA_FULFILLMENT_API_KEY

const preCheckoutComplete = async (req, res) => {
    // console.log("ooooooooooo preCheckoutComplete oooooooooooooooooooo ", req.body)
    // const paymentStatus = await checkPaywayTranStatusApi(fulfillmentMerchantId, fulfillmentApiKey, req.body.tranId);

    // if (paymentStatus.data.payment_status_code == 0) {
    //     const resp = await completePreAuthApi(req.body.tranId, req.body.deliveryFee);
    //     return res.json(resp)
    // } else {
    //     return res.json({ error: 'Booking is not yet paid' })
    // }

}

module.exports = preCheckoutComplete