// const moment = require("moment");
// const db = require("../../../models");

// // const generateFulFillmentHash = require("../../../utils/payment/aba/generateFulFillmentHash");
// const generateFulFillmentHash = require("../../../utils/payment/aba/generateFulfillmentHash")
// const getTransactionId = require("../../../utils/payment/getTransactionId");

// const merchantId = process.env.ABA_FULFILLMENT_MERCHANT_ID;
// const baseUrl = process.env.ABA_BASE_URL;


// const getDeliveryFeeApi = require("../../../api/grab/getDeliveryFeeApi")


const preCheckout = async (req, res, next) => {
  // const transactionId = getTransactionId();
  // const hostUrl = process.env.HOST_URL;
  // const type = "pre-auth"

  // const authorziationStr = req.headers["authorization"]
  // const accessToken =  authorziationStr == undefined ? "check-out-without-token" : authorziationStr.split(" ")[1]

  // const returnUrl = Buffer.from(
  //   `${hostUrl}/payment/aba/pre-checkout/callback/success?token=${accessToken}`
  // ).toString("base64");

  // const { orderId } = req.query;

  // const orderTb = await db.Order.findOne({
  //   where: { id: orderId },
  //   include: [
  //     {
  //       model: db.Shop,
  //       as: "shop",
  //     },
  //     {
  //       model: db.ShopAddress,
  //       as: "shopAddress",
  //     },
  //     {
  //       model: db.BuyerAddress,
  //       as: "buyerAddress",
  //     },
  //   ],
  // });

  // // https://test.dobpi.shop/tracking/eyJvcmRlcklkIjozMjEyLCJzaG9wSWQiOiAxfQ==?popup

  // if (orderTb == null) {
  //   return res.render("error", {
  //     message: "Order ID not found",
  //     error: {
  //       status: "error",
  //       stack: "Can't find order",
  //     },
  //   });
  // }

  // // const paymentOptionTb = await db.PaymentOption.findOne({
  // //   where: { id: orderTb.paymentOptionId },
  // // });

  // // console.log("paymentOptionTb", paymentOptionTb)

  // // if (paymentOptionTb == null) {
  // //   return res.render("error", {
  // //     message: "Payment option not found",
  // //     error: {
  // //       status: "error",
  // //       stack: "Can't find order",
  // //     },
  // //   });
  // // }
  // // const paymentOptionCode = paymentOptionTb.code

  // const paymentOptionCode = "abapay_khqr"
  // let successUrl = `${process.env.SELLER_PORTAL}/${orderTb.shopId}/order?activeTab=processing`
  // let cancelUrl = ""
  // let abaScript = "https://checkout.payway.com.kh/plugins/checkout2-0.js?hide-close=2";

  // if (process.env.NODE_ENV != "production") {
  //   abaScript = "https://checkout.payway.com.kh/plugins/checkout2-0-dev.js?hide-close=2";
  // }

  // if (orderTb.shopAddress == null || orderTb.shopAddress.location == null) {
  //   throw Error("Shop doesn't has pick-up address location")
  // }

  // let deliveryResp = {}

  // if (orderTb.buyerAddress != null) {
  //   deliveryResp = await getDeliveryFeeApi({
  //     origin: {
  //       latitude: orderTb.shopAddress.location.lat,
  //       longitude: orderTb.shopAddress.location.lng
  //     },
  //     destination: {
  //       latitude: orderTb.buyerAddress.location.lat,
  //       longitude: orderTb.buyerAddress.location.lng
  //     }
  //   });
  // }

 

  // if (deliveryResp.error) {
  //   return res.render("error", {
  //     message: deliveryResp.error,
  //     error: {
  //       status: "error",
  //       stack: deliveryResp.error,
  //     },
  //   });
  // }

  // let deliveryFeePaidToPartner = deliveryResp.deliveryFee
  // let deliveryFeePaidToDobpi = parseFloat(process.env.DOBPI_DELIVERY_CHARGE);
  // let deliveryFee = deliveryFeePaidToPartner + deliveryFeePaidToDobpi

  // let reqTime = moment().format("YYYYMMDDHHmmss");

  // let hash = generateFulFillmentHash(
  //   reqTime,
  //   transactionId,
  //   orderTb.customerContact,
  //   deliveryFee,
  //   type,
  //   paymentOptionCode,
  //   returnUrl,
  //   successUrl,
  //   cancelUrl
  // );

  // const resp = {
  //   baseUrl,
  //   reqTime,
  //   merchantId,
  //   transactionId,
  //   phone: orderTb.customerContact,
  //   amount: deliveryFee,
  //   paymentOption: paymentOptionCode,
  //   type,
  //   returnUrl,
  //   successUrl,
  //   cancelUrl,
  //   hash,
  //   abaScript,
  // };


  // await db.Order.update({ preAuthTranId: transactionId }, { where: { id: orderId } });

  // return res.render("payment/v1/aba-preauth.ejs", resp);
};

module.exports = preCheckout;
