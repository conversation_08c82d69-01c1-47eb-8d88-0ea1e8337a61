// const preCallbackSuccessRepo = require("../../../repo/payway/preCallbackSuccess")

const preCallbackSuccess = async (req, res, next) => {
  // console.log("ooooooooooooooo preCallbackSuccess ooooooooooooooo")
  // const { tran_id, apv, status } = req.body;
  // const token = req.query.token;
  // const resp = preCallbackSuccessRepo({tran_id, apv,status, token})
  // return res.json(resp)
};

module.exports = preCallbackSuccess;
