const paymentCheckoutService = require("@services/payway/checkoutRest")

const checkoutRest = async (req, res, next) => {

  const { orderId, paymentMethod } = req.params
  const receiptFile = req.query['receiptFile']
 
  const payment = await paymentCheckoutService(orderId, paymentMethod, receiptFile)

  if (payment.hasOwnProperty('errorMessage')) {
    return res.error(
      payment.errorMessage,
      res.HttpStatus.BAD_REQEUST,
      {
        status: "error",
        stack: payment.stack,
      },
    );
  }

  return res.success(payment)

};

module.exports = require("@asyncHandler")(checkoutRest);
