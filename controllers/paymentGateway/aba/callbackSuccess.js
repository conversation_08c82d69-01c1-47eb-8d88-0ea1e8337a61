const merchantId = process.env.ABA_MERCHANT_ID;
const apiKey = process.env.ABA_API_KEY;

const db = require("@db");
const checkPaymentStatusApi = require("@apis/payway/checkPaymentStatusApi")

const formatPhoneNumber = require("@apis/plasgate/formatPhoneNumber")
const alertOrderToAdmin = require("@services/telegram/alertOrderToAdmin")
const moment = require('moment');


const callbackSuccess = async (req, res, next) => {
  const { tran_id, apv, status } = req.body;

  const paymentResp = await checkPaymentStatusApi(merchantId, apiKey, tran_id);

  if (['PRE-AUTH', 'APPROVED'].includes(paymentResp.data.payment_status)) {
    const orderTb = await db.Order.findOne({
      attributes: { exclude: ["userId", "productOptionId", "updatedAt"] },
      where: { tranId: tran_id },
      include: [
        {
          model: db.ProductOptionV2, as: 'productOptionV2',
          attributes: { exclude: ["createdAt", "updatedAt", "sort", "productId"] },
          include: [
            {
              model: db.Product, as: 'product',
              attributes: { exclude: ["createdAt", "updatedAt", "categoryId", "sort", "status", "amount"] },
              include: [
                {
                  model: db.Category, as: 'category',
                  attributes: { exclude: ["createdAt", "updatedAt", "sort", "status"] },

                }

              ]
            }

          ]
        },
        {
          model: db.OrderDetail, as: 'orderDetails',
          attributes: { exclude: ["qty", "orderId", "createdAt", "updatedAt"] },

        },
        {
          model: db.OrderTracking,
          as: 'orderTrackings',
          attributes: { exclude: ["updatedAt", "createdAt"] }
        },
      ]
    })

    if (orderTb) {

      await db.Order.update({ paymentStatus: "PAID" }, { where: { tranId: tran_id } })

      let languageLabel = "English";

      switch (orderTb.language) {
        case "en": languageLabel = "English"; break;
        case "kh": languageLabel = "Khmer"; break;
        case "cn": languageLabel = "Chinese Simplify"; break;
        case "tw": languageLabel = "Chinese Tranditional"; break;
        case "vi": languageLabel = "Vietnamese"; break;
      }

      const productAddOn = await db.OrderDetail.findAll({
        attributes: ["nameEn", "nameKm", "nameVi", "nameCn", "nameTw", "amount"],
        where: {
          id: orderTb.id
        },
        raw: true
      })

      const addOnAmount = productAddOn.reduce((sum, addOn) => sum + ((addOn.amount *  addOn.qty) || 0), 0);

      const message = `
        🚨 *SERVICE BOOKING ALERT* 🚨

👤 *Customer Information*
        *Name:* ${orderTb.customerFirstName} ${orderTb.customerLastName}  
        *Phone:* ${formatPhoneNumber(orderTb.customerPhone)}  
        *Email:* ${orderTb.customerEmail}
        *Language:* ${languageLabel}
        *Note:* ${orderTb.note}

🛠️ *Service Information*
        *Category Name:* ${orderTb.productOptionV2.product.category.nameEn}  
        *Product Name:* ${orderTb.productOptionV2.product.nameEn}  
        *Product Option:* ${orderTb.productOptionV2.nameEn}  
        *Add-on Services:* ${"N/A"}  
        *Amount:* $${(orderTb.amount + addOnAmount).toFixed(2)} 
        *Coupon Applied:* ${orderTb.couponCode} 
        *Discount:* $${orderTb.discount.toFixed(2)}
        *Service Fee:* $${orderTb.serviceFee.toFixed(2)}
        *Transport Fee:* $${orderTb.transportFee.toFixed(2)}
        *VAT:* $${orderTb.vatFee.toFixed(2)}
        *Total Fee:* $${orderTb.totalPayableAmount.toFixed(2)}
        *Payment Method:* ${orderTb.paymentMethod}
        *Payment Status:* ${orderTb.paymentStatus}
         
📍 *Location Details*
        *Address:* ${orderTb.address}  
        *Floor:* ${orderTb.floorNum}  
        *Room:* ${orderTb.roomNum}  

📅 *Schedule*
        *Start Date:* ${moment(orderTb.scheduleStartDate).format('DD-MM-YYYY')}
        *Start Time:* ${moment(orderTb.scheduleStartDate).format('h:mm A')}
        *End Time:* ${moment(orderTb.scheduleStartDate).add(orderTb.productOptionV2.duration, 'hours').format('h:mm A')}
    `;

      alertOrderToAdmin(message);



      return res.json({ message: "Payment is successful" })
    }

  }

  return res.json({ message: "Payment status is pending" })
};

module.exports = callbackSuccess;
