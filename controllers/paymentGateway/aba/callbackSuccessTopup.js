const merchantId = process.env.ABA_MERCHANT_ID;
const apiKey = process.env.ABA_API_KEY;

const db = require("@db");
const checkPaymentStatusApi = require("@apis/payway/checkPaymentStatusApi")
const alertPaymentToAdmin = require("@services/telegram/alertPaymentToAdmin")
const formatPhoneNumber = require("@apis/plasgate/formatPhoneNumber");
const moment = require("moment/moment");

const callbackSuccessTopup = async (req, res, next) => {

    try {
        const { tran_id, apv, status } = req.body;

        const paymentResp = await checkPaymentStatusApi(merchantId, apiKey, tran_id);

        if (paymentResp.data.payment_status == "APPROVED") {
            const topupTb = await db.Topup.findOne({ where: { tranId: tran_id } })

            if (topupTb) {
                topupTb.status = "PAID";
                await topupTb.save();
                const userTb = await db.User.findOne({ where: { id: topupTb.userId } })
                userTb.balance = userTb.balance + topupTb.credit
                await userTb.save()

                const paymentMethodTb = await db.PaymentMethod.findOne({ where: { code: topupTb.paymentMethod } })

                const message = `
                🚨 *SERVICE BOOKING ALERT* 🚨
👤 *Customer Information*
        *Name:* ${userTb.firstName} ${userTb.lastName}  
        *Phone:* ${formatPhoneNumber(userTb.username)}  

🛠️ *Top up information*
        *Transaction ID:* ${topupTb.tranId}
        *Payment Method:* ${paymentMethodTb.nameEn} 
        *Paid Amount:* $${topupTb.amount.toFixed(2)}  
        *Credit:* ${topupTb.credit.toFixed(2)} POINTS\n  
        *Transaction Date:* ${moment(topupTb.tranInitDate).format("DD-MM-YYYY hh:mm A")}  
        *Expiration Date:* ${topupTb.expDate == null ? "Never Expire" : moment(topupTb.expDate).format("DD-MM-YYYY hh:mm A")}\n
        *Current Balance:* $${userTb.balance.toFixed(2)}   
        *Remark:* ${topupTb.remarkEn}`;

                alertPaymentToAdmin(message);

                return res.json({ message: "Payment is successful" })
            }

        }

        return res.json({ message: "Payment status is pending" })
    } catch (err) {
        console.log(err)
    }

};

module.exports = callbackSuccessTopup;
