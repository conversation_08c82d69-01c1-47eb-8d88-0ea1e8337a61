// const moment = require("moment");
// const db = require("../../../models");
// const { getIO } = require('../../../utils/socketIOConfig');
// const { alertSubscriptionToAdmin } = require('../../../utils/botUtil');

// let dnsDomain = process.env.DOBPI_BASE_DOMAIN

const fulfillmentCallbackSuccess = async (req, res, next) => {
    // try {
    //     const { tran_id, apv, status } = req.body;
    //     const token = req.params.token;
    //     const shopId = req.params.shopId;
    //     const packageId = req.params.packageId;

    //     const io = getIO();

    //     if (status == 0) {
    //         console.log("db.Order.update");
    //         const shopPackageTb = await db.ShopPackage.findOne({ where: { id: packageId } })
    //         const shopTb = await db.Shop.findOne({ where: { id: shopId } })
    //         const curExpDate = moment(shopTb?.expDate);

    //         const subPackageId = shopPackageTb?.id
    //         const subPackage = shopPackageTb?.package
    //         const subAmount = shopPackageTb?.amount;
    //         const subDate = moment()
    //         let remainingDay = curExpDate.diff(subDate, 'days')

    //         const expDate = moment().add(shopPackageTb?.duration, shopPackageTb?.durationType)
    //             .add(shopPackageTb?.durationBonus, shopPackageTb?.durationBonusType)
    //             .add(remainingDay > 0 ? remainingDay : 0, 'days')

    //         console.log({ subPackage, subAmount, subDate, expDate })

    //         await db.Shop.update(
    //             { subDate, expDate, shopPackageId: subPackageId },
    //             { where: { id: shopId } }
    //         );
    //         let totalInvocie = await db.ShopSubscription.count({ where: { shopId } })
    //         let invoiceNumber = `INV${shopId}-` + (totalInvocie + 1).toString().padStart(3, '0');

    //         await db.ShopSubscription.create({ paymentOption: "ABA-PAY", trxnId: tran_id, apv, shopId, invoiceNumber, package: subPackage, amount: subAmount, expDate, subDate })
    //         io.emit('subscription-success', { shopId, token });
    //         alertSubscriptionToAdmin(process.env.SUBSCRIPTION_CHANNEL,
    //             invoiceNumber,
    //             shopTb.domain != null ? dnsDomain.replace("subdomain", shopTb.domain) : "N/A",
    //             `Phone 1 = ${shopTb.phone1 == undefined ? "N/A" : shopTb.phone1 }, Phone 2 =  ${shopTb.phone2==undefined? "N/A": shopTb.phone2}`,
    //             shopTb.id,
    //             shopTb.name,
    //             subPackage,
    //             `$ ${subAmount} USD`,
    //             moment(curExpDate).format("YYYY-MM-DD hh:mm a"),
    //             moment(subDate).format("YYYY-MM-DD hh:mm a"),
    //             moment(expDate).format("YYYY-MM-DD hh:mm a"));
    //         return res.json({ message: "Success" });
    //     } else {
    //         throw Error("Transaction is not completed");
    //     }
    // } catch (err) {
    //     console.log(err)
    //     return res.json({ error: err.message });
    // }
};

module.exports = fulfillmentCallbackSuccess;
