const db = require("@db");

const checkPaymentStatus = async (req, res, next) => {
  try {
    let orderTb = await db.Order.findOne({ where: { id: req.body.orderId } });

    if (orderTb == null) {
      throw Error("Order ID not found");
    }

    return res.json({ isPaid: orderTb.isPaid, amount: orderTb.totalAmount });
  } catch (err) {
    return res.json({ error: err.message });
  }
};

module.exports = checkPaymentStatus;
