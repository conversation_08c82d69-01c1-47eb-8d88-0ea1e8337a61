

const androidDeeplink = async (req, res, next) => {

    return res.json(
        [
            {
                "relation": [
                    "delegate_permission/common.handle_all_urls",
                    "delegate_permission/common.get_login_creds"
                ],
                "target": {
                    "namespace": "android_app",
                    "package_name": "suntel.beasy.app",
                    "sha256_cert_fingerprints": [
                        "D4:42:6D:98:D6:D9:1F:4E:2C:CF:DB:05:CA:65:73:0A:40:06:F4:32:A7:2D:F4:A5:A4:A3:BE:86:AC:80:61:8D",
                        "D7:77:39:FB:1E:A8:5A:CC:1A:3F:B2:16:9D:4C:ED:C6:10:7E:47:21:37:AA:2A:54:EA:63:89:C2:CE:02:55:04"
                    ]
                }
            }
        ]
    )

}

module.exports = androidDeeplink

