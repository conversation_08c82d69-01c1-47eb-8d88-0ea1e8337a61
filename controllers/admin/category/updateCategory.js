
const db = require("@db")

const updateCategory = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const resp = await db.Category.update(req.body, { where: { id: req.body.id } })

    if (resp[0] == 0) {
        return res.error("Category is not found!", res.HttpStatus.NOT_FOUND )
    }

    const newCategory = await db.Category.findOne({ where: { id: req.body.id } })


    return res.success(newCategory)

}

module.exports = require("@asyncHandler")(updateCategory)