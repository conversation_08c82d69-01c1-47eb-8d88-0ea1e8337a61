
const db = require("@db")
const { Op } = require("sequelize")

const rearrangeCategory = async (req, res, next) => {

    const { id, sort } = req.body

    let categoryTbs = await db.Category.findAll(
        {
            where: {
                id: {
                    [Op.ne]: id  // not equal to 1
                }
            },
            raw: true,
            attributes: ["id", "sort"]
        }
    )

    console.log(" categoryTbs ", categoryTbs.length)

    for (let i = 0; i < categoryTbs.length; i++) {

        console.log(" i == sort ", i == sort)

        if (i == sort) {
            categoryTbs.splice(i, 0, { id, sort });
        } else {
            categoryTbs[i].sort = i;
        }
    }

    console.log(" categoryTbs ", categoryTbs.length)

    await Promise.all(
        categoryTbs.map(item =>

            db.Category.update(
                { sort: item.sort },
                { where: { id: item.id } }
            )
        )
    ).then(async () => {
        categoryTbs = await db.Category.findAll(
            {
                attributes: ["id", "nameEn", "iconUrl", "status", "sort", "updatedAt"],
                order: [['sort', 'ASC']]
            }
        )

        return res.success(categoryTbs)
    });


}

module.exports = require("@asyncHandler")(rearrangeCategory)