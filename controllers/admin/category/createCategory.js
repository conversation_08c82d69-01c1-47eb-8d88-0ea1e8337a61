 
const db = require("@db") 

const createCategory = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const resp = await db.Category.create(req.body)

    return res.success(resp)

}

module.exports = require("@asyncHandler")(createCategory)