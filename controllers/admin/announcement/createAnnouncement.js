const db = require("@db")

const createAnnouncement = async (req, res, next) => {

  const authHeader = req.headers['authorization'];
  const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

  if (!token) {
    return res.status(401).json({ message: 'Token not provided' });
  }

  const payload = {
    thumbnailUrl: req.files?.thumbnailUrl?.[0]?.filename || null,
    bannerUrlEn: req.files?.bannerUrlEn?.[0]?.filename || null,
    bannerUrlKh: req.files?.bannerUrlKh?.[0]?.filename || null,
    bannerUrlZh: req.files?.bannerUrlZh?.[0]?.filename || null,
    ...req.body
  };

  console.log(" payload", payload)

  const resp = await db.Announcement.create(payload)

  return res.success(resp)

}

module.exports = require("@asyncHandler")(createAnnouncement)