
const db = require("@db")
 

const updateBanner = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

     await db.Banner.update(req.body, { where: { id: req.body.id } })
     const bannerTb = await db.Banner.findOne({where:{id: req.body.id}})

    return res.success(bannerTb)

}

module.exports = require("@asyncHandler")(updateBanner)