 
const db = require("@db")
const listBannerService = require("@services/banner/listBanner")


const listBanner = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const resp = await listBannerService(req.query,)

    return res.success(resp)

}

module.exports = require("@asyncHandler")(listBanner)