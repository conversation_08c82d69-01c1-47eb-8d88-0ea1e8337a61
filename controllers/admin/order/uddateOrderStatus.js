const db = require("@db")

const moment = require('moment');
const jwt = require('jsonwebtoken');
const formatPhoneNumber = require("@apis/plasgate/formatPhoneNumber")
const completePreAuthApi = require("@apis/payway/completePreAuthApi")
const alertOrderToAdmin = require("@services/telegram/alertOrderToAdmin")

const uddateOrderStatus = async (req, res, next) => {

    const bulkOrderId = req.body.bulkOrderId
    const newOrderStatus = req.body.status
    const userTb = req.user


    const orderDetailTb = await db.Order.findOne({
        attributes: { exclude: ["userId", "productOptionId", "updatedAt"] },
        where: { bulkOrderId },
        include: [
            {
                model: db.ProductOption, as: 'productOption',
                attributes: { exclude: ["createdAt", "updatedAt", "sort", "productId"] },
                include: [
                    {
                        model: db.Product, as: 'product',
                        attributes: { exclude: ["createdAt", "updatedAt", "categoryId", "sort", "status", "amount"] },
                        include: [
                            {
                                model: db.Category, as: 'category',
                                attributes: { exclude: ["createdAt", "updatedAt", "sort", "status"] },

                            }

                        ]
                    }

                ]
            },
            {
                model: db.OrderDetail, as: 'orderDetails',
                attributes: { exclude: ["qty", "orderId", "createdAt", "updatedAt"] },

            },
            {
                model: db.OrderTracking,
                as: 'orderTrackings',
                attributes: { exclude: ["updatedAt", "createdAt"] }
            },
        ]
    })

    if (!orderDetailTb) {
        return res.error("Order not found!")
    }

    let hourLabel;
    let languageLabel;

    switch (userTb.language) {
        case 'KH':
            hourLabel = 'ម៉ោង'; // Khmer
            languageLabel = 'KHMER Language';
            break;
        case 'ZH':
            hourLabel = '小时'; // Chinese
            languageLabel = 'CHINESE Language';
            break;
        case 'EN':
        default:
            hourLabel = 'hour'; // Default to English
            languageLabel = 'ENGLISH Language';
    }

    const message = `
            ⚠️ *CANCELLATION REQUESTED* ⚠️
    
    👤 *Customer Information*
            *Name:* ${orderDetailTb.customerFirstName} ${orderDetailTb.customerLastName}  
            *Phone:* ${formatPhoneNumber(orderDetailTb.customerPhone)}  
            *Email:* ${orderDetailTb.customerEmail}
            *Language:* ${languageLabel}
            *Note:* ${orderDetailTb.note}
    
    🛠️ *Service Information*
            *Category Name:* ${orderDetailTb.productOption.product.category.nameEn}  
            *Product Name:* ${orderDetailTb.productOption.product.nameEn}  
            *Product Option:* ${orderDetailTb.productOption.nameEn}  
 
            *Coupon Applied:* ${orderDetailTb.couponCode} 
            *Discount:* $${orderDetailTb.discount.toFixed(2)}
            *Service Fee:* $${orderDetailTb.serviceFee.toFixed(2)}
            *Transport Fee:* $${orderDetailTb.transportFee.toFixed(2)}
            *VAT:* $${orderDetailTb.vatFee.toFixed(2)}
            *Total Fee:* $${orderDetailTb.totalAmount.toFixed(2)}
            *Payment Method:* ${orderDetailTb.paymentMethod}
            *Payment Status:* ${orderDetailTb.paymentStatus}
             
    📍 *Location Details*
            *Address:* ${orderDetailTb.address}  
            *Floor:* ${orderDetailTb.floorNum}  
            *Room:* ${orderDetailTb.roomNum}  
    
    📅 *Schedule*
            *Start Date:* ${moment(orderDetailTb.scheduleStartDate).format('DD-MM-YYYY')}
            *Start Time:* ${moment(orderDetailTb.scheduleStartDate).format('h:mm A')}
            *End Time:* ${moment(orderDetailTb.scheduleStartDate).add(orderDetailTb.productOption.duration, 'hours').format('h:mm A')}
        `;

    console.log("  orderDetailTb.totalPayableAmount ", orderDetailTb.status)

    switch (newOrderStatus) {
        case 'ACCEPTED':

            if (['PENDING'].includes(orderDetailTb.status)) {
                orderDetailTb.status = newOrderStatus
                await orderDetailTb.save()
                await orderDetailTb.reload()
            } else {
                return res.error("Order can not be " + newOrderStatus)
            }

            break;

        case 'IN-PROGRESS':
            if (['ACCEPTED'].includes(orderDetailTb.status)) {
                orderDetailTb.status = newOrderStatus
                await orderDetailTb.save()
                await orderDetailTb.reload()
            } else {
                return res.error("Order can not be " + newOrderStatus)
            }


            break;

        case 'COMPLETED':
            if (['IN-PROGRESS'].includes(orderDetailTb.status)) {
                orderDetailTb.status = newOrderStatus
                await orderDetailTb.save()
                await orderDetailTb.reload()
            } else {
                return res.error("Order can not be " + newOrderStatus)
            }

            break;

        case 'CANCELLED':

            if (['PENDING'].includes(orderDetailTb.status)) {
                orderDetailTb.status = newOrderStatus
                await orderDetailTb.save()
                await orderDetailTb.reload()
            } else {
                return res.error("Order can not be " + newOrderStatus)
            }

            break;

        case 'REJECTED':

            if (['PENDING'].includes(orderDetailTb.status)) {
                orderDetailTb.status = newOrderStatus
                await orderDetailTb.save()
                await orderDetailTb.reload()
            } else {
                return res.error("Order can not be " + newOrderStatus)
            }

            break;
    }

    // await completePreAuthApi(orderDetailTb.tranId, orderDetailTb.totalPayableAmount)

    return res.success("Your order is " + newOrderStatus)
}

module.exports = require("@asyncHandler")(uddateOrderStatus)