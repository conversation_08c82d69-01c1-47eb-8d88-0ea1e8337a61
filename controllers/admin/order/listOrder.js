const db = require("@db")


const listOrder = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }


const [results] = await db.sequelize.query(`
  SELECT 
    bulk_order_id AS bulkOrderId , 
    status, 
    COUNT(*) AS totalCount,
    MAX(updated_at) AS lastUpdatedAt
  FROM orders
  GROUP BY bulkOrderId, status
`);

    return res.success(results)
}

module.exports = require("@asyncHandler")(listOrder)