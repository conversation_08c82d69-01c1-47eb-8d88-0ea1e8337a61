 
const db = require("@db") 

const listProduct = async (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1]; // Format: Bearer <token>

    if (!token) {
        return res.status(401).json({ message: 'Token not provided' });
    }

    const productTb = await db.Product.findAll({})

    return res.success(productTb)

}

module.exports = require("@asyncHandler")(listProduct)