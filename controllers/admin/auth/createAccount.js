const { Op } = require('sequelize');
const db = require("@db")
const jwt = require('jsonwebtoken');
const moment = require("moment")
const bcrypt = require('bcrypt')

const createAccount = async (req, res) => {
    const { username, password, firstName, lastName } = req.body;

    // Check for missing fields
    if (!username || !password || !firstName || !lastName) {
        return res.status(400).json({ message: 'All fields are required' });
    }

    console.log(" { username, password, firstName, lastName } ", { username, password, firstName, lastName })

    try {
        // Check if user exists
        const existingUser = await db.User.findOne({ where: { username } });

        if (existingUser) {
            return res.status(400).json({ message: 'User already exists' });
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Create new user
        const newUser = {
            username,
            password: hashedPassword,
            firstName,
            lastName,
            type: 'ADMIN',
            status: 'ACTIVE'
        };

        const newUserTb = await db.User.create(newUser)

        // Generate JWT token
        const token = jwt.sign(
            { userId: newUserTb.id, username: newUserTb.username },
            process.env.JWT_SECRET,
            { expiresIn: '30d' }
        );

        return res.status(201).json({ token });

    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Server error' });
    }
};

module.exports = require("@asyncHandler")(createAccount)