const { Op } = require('sequelize');
const db = require("@db")
const jwt = require('jsonwebtoken');
const moment = require("moment")
const bcrypt = require("bcrypt")

const login = async (req, res, next) => {
    const { username, password } = req.body

    // check if there is existing account associated with the phone
    const existingAccountVerified = await db.User.count({
        where: {
            username: {
                [Op.like]: `%${username}`
            }, status: { [Op.in]: ["ACTIVE"] }
        }
    })

    if (existingAccountVerified == 0) {
        return res.error('Account not found!', res.HttpStatus.NOT_FOUND)
    }

    const userTb = await db.User.findOne({
        where: {
            username: {
                [Op.like]: `%${username}`,
            },
        }
    })

    console.log(" user ", userTb)

    if (!userTb) {
        return res.status(404).json({ message: 'User not found' });
    }

    // 2. Compare password
    const isMatch = await bcrypt.compare(password, userTb.password);

    if (!isMatch) {
        return res.status(401).json({ message: 'Invalid password' });
    }




    const userInfo = { id: userTb.id, username: userTb.username }; // user payload
    const secret = process.env.JWT_SECRET; // use env in production
    const expiresIn = '30d'; // or '15m', '7d', etc.

    const token = jwt.sign(userInfo, secret, { expiresIn });

    return res.success({ token: token, userInfo: userTb }, "User account is active")

}


module.exports = require("@asyncHandler")(login)