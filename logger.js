// middleware/logger.js
const logger = require('./logEngine');
const { v4: uuidv4 } = require('uuid');

function requestLogger(req, res, next) {
  const traceId = req.headers['x-trace-id'] || uuidv4();
  req.traceId = traceId;

  const start = Date.now();
  const chunks = [];

  const originalSend = res.send;

  res.send = function (body) {
    const duration = Date.now() - start;

    const logPayload = {
      traceId,
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration,
      request: {
        headers: req.headers,
        body: req.body
      },
      response: {
        body: body
      },
      timestamp: new Date().toISOString()
    };

    logger.info(logPayload);

    return originalSend.call(this, body);
  };

  next();
}

module.exports = requestLogger;