const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'dev';
dotenv.config({ path: `.env.${env}` });

require('module-alias/register');
const responseWrapper = require('./routes/responseWrapper');

// require("@services/scheduler/payway")

var express = require('express');
var cors = require('cors')
var path = require('path');
var cookieParser = require('cookie-parser');
var logger = require('morgan');


const { Telegraf } = require('telegraf')
const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN)
const sendMessage = require("@services/telegram/sendMessage")
const TELEGRAM_LUCKY_DRAW_GROUP_ID = process.env.TELEGRAM_LUCKY_DRAW_GROUP_ID

// bot.launch({ allowed_updates: ['chat_member'] })

// Listen for message command - /groupid
bot.hears("/groupid", (ctx) => {
  const message = `Group ID: ${ctx.update.message.chat.id}\nGroup Name: ${ctx.update.message.chat.title}`;
  sendMessage(message, TELEGRAM_LUCKY_DRAW_GROUP_ID)
  // sendMessage.sendGroupInfo({
  //   groupId: ctx.update.message.chat.id,
  //   groupName: ctx.update.message.chat.title,
  // });

});

// Detect bot start with referral code
bot.start((ctx) => {
  try {
    const parts = ctx.message.text.split(' ');
    const referral = parts[1] || null;
    const user = ctx.from;
    console.log(`bot.start`, ctx);
    if (referral) {
      console.log(`[Referral] ${user.id} (${user.first_name}) used code: ${referral}`);
      ctx.reply(`Welcome, ${user.first_name}! You used referral code: ${referral}`);
      // Save referral to DB
    } else {
      ctx.reply(`Welcome, ${user.first_name}!`);
    }
  } catch (err) {
    console.log(" bot.start error ", err)
  }
});

bot.on('chat_member', async (ctx) => {
  console.log('[chat_member event]');
  console.log(JSON.stringify(ctx.update, null, 2));
});

bot.on('my_chat_member', async (ctx) => {
  console.log('[my_chat_member event]');
  console.log(JSON.stringify(ctx.update, null, 2));
});

bot.command('create_link', async (ctx) => {
  const chatId = ctx.chat.id;

  // Optional: validate if it's a group or supergroup
  if (ctx.chat.type !== 'group' && ctx.chat.type !== 'supergroup') {
    return ctx.reply('❌ This command can only be used in groups.');
  }

  try {
    const inviteLink = await ctx.telegram.createChatInviteLink(chatId, {
      name: `ref-${ctx.from.username || ctx.from.id}`,
      creates_join_request: false, // set true if you want approval flow
      expire_date: Math.floor(Date.now() / 1000) + 86400, // expires in 1 day
      member_limit: 5
    });

    ctx.reply(`✅ Here's your invite link:\n${inviteLink.invite_link}`);
  } catch (err) {
    console.error('Error creating invite link:', err);
    ctx.reply('❌ Failed to create invite link. Make sure I am an admin.');
  }
});

// bot.on('message', async (ctx) => {
//   const user = ctx.from;
//   const text = ctx.message.text;
//   const chatId = ctx.chat.id;
//   const inviter = ctx.update.invite_link;

//   console.log(`📩 Message from ${user.username || user.first_name}:`, ctx.update.message);

//   const name = `${user.first_name || ''} ${user.last_name || ''}`;
//   const mention = `<a href="tg://user?id=${user.id}">${name}</a>`;

//   const htmlMessage = `
// 🎉🎉🎉 Hello ${mention} 🎉🎉🎉

// Welcome to the group! Please send me a direct message to get your referral code and stand a chance to win a prize in our lucky draw.

// Thanks for joining the <b>bEasy Competition</b> — and good luck! 🎁✨

// 👉 <a href="https://t.me/BEasyAppBot?start=${user.id}">Click here now to register and stand a chance to win in our lucky draw</a>
// `;

//   ctx.reply(htmlMessage, { parse_mode: 'HTML' });
//   await bot.telegram.sendMessage(user.id, 'Hello from bEasy bot!');
//   // Optional: reply to every message
//   // await ctx.reply(`👋 Hi ${user.first_name}, I saw your message: "${text}"`);
// });

var indexRouter = require('./routes/index');

var app = express();
app.use(cors({
  origin: '*',        // Allow all origins
  methods: '*',       // Allow all methods
  allowedHeaders: '*' // Allow all headers
}))

app.use(logger('dev'));
app.use(express.json());
app.use(require('./logger'));
app.use(responseWrapper)
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));

// Set view engine
app.set('view engine', 'ejs');

// Optional: set views directory if it's not the default 'views'
app.set('views', __dirname + '/views');

// Main App Route
app.use('/api', indexRouter);
app.get("/.well-known/apple-app-site-association", require("@controllers/marketing/appflyer/iosDeeplink"));
app.get("/.well-known/assetlinks.json", require("@controllers/marketing/appflyer/androidDeeplink"));

app.get('/privacy-policy', (req, res, next) => {
  res.sendFile(path.join(__dirname, 'public', 'privacy-policy.html'));
});

app.get('/term-and-service', (req, res, next) => {
  res.sendFile(path.join(__dirname, 'public', 'term-and-service.html'));
});

// app.use((req, res, next) => {
//   if (req.path === '/.well-known/apple-app-site-association') {
//     res.type('application/json');

//     return res.json({
//       "applinks": {
//         "apps": [],
//         "details": [
//           {
//             "appIDs": ["J7BZ7KA58L.suntel.beasy.app"],
//             "paths": ["/success/*", "/success*"],
//             "components": [
//               {
//                 "/": "/*"
//               }

//             ]
//           }
//         ]
//       },
//       "webcredentials": {
//         "apps": ["J7BZ7KA58L.suntel.beasy.app"]
//       }
//     })
//   }
//   next();
// });






// require('./services/telegram/sendMessage')('Testing message')

// GLOBAL Error handler
app.use((err, req, res, next) => {
  console.error('[Global Error]', err); // or use your logger here

  res.status(err.status || 500).json({
    status: err.status || 500,
    message: err.message || 'Internal Server Error',
    traceId: req.traceId || undefined  // optional: trace ID support
  });
});

// NOT Found Handler
app.use((req, res, next) => {
  res.status(404).json({
    code: 404,
    message: 'API endpoint not found'
  });
});


 
// if (process.env.NODE_ENV == 'pro') {
//   require("@utils/generateBulkOrderKey")()
// }

module.exports = app;
