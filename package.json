{"name": "beasy-api-v2", "version": "0.0.0", "private": true, "scripts": {"start:pro": "NODE_ENV=pro node ./bin/www", "start:dev": "NODE_ENV=dev nodemon ./bin/www", "start:stg": "NODE_ENV=stg node ./bin/www"}, "dependencies": {"axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "casbin": "^5.38.0", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "debug": "~2.6.9", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "~4.16.1", "firebase-admin": "^13.4.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.8", "module-alias": "^2.2.3", "moment": "^2.30.1", "morgan": "~1.9.1", "multer": "^2.0.0", "mysql2": "^3.14.1", "node-cron": "^4.1.0", "rotating-file-stream": "^3.2.6", "sequelize": "^6.37.7", "telegraf": "^4.16.3", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/supertest": "^6.0.3", "cross-env": "^10.0.0", "jest": "^30.0.5", "nodemon": "^3.1.10", "supertest": "^7.1.4"}, "_moduleAliases": {"@db": "models", "@controllers": "controllers", "@services": "services", "@apis": "apis", "@utils": "utils", "@asyncHandler": "controllers/asyncHandler", "@authUser": "controllers/auth/authUser", "@authAdmin": "controllers/auth/authAdmin", "@constants": "constants"}}