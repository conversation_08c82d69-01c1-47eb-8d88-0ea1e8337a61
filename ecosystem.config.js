module.exports = {
  apps: [
    {
      name: 'beasy-api',
      script: 'app.js', // or index.js, main entry
      env_dev: {
        NODE_ENV: 'dev'
      },
      env_stg: {
        NODE_ENV: 'stg'
      },
      env_pro: {
        NODE_ENV: 'pro'
      }
    },
    // {
    //   script: './service-worker/',
    //   watch: ['./service-worker']
    // }

  ],

  deploy: {
    production: {
      user: 'SSH_USERNAME',
      host: 'SSH_HOSTMACHINE',
      ref: 'origin/master',
      repo: 'GIT_REPOSITORY',
      path: 'DESTINATION_PATH',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
