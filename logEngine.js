// logger.js
const { createLogger, format, transports } = require('winston');
require('winston-daily-rotate-file');

const dailyRotate = new transports.DailyRotateFile({
  filename: 'logs/api-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  maxFiles: '30d',
  zippedArchive: true,
  level: 'info',
});

const logger = createLogger({
  format: format.combine(
    format.timestamp(),
    format.json()
  ),
  transports: [dailyRotate, new transports.Console()],
});

module.exports = logger;